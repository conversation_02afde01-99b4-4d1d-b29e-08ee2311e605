<template>
  <div class="web-push-test">
    <div class="page-header">
      <h1>Web推流测试</h1>
      <p>测试腾讯云WebRTC推流功能</p>
    </div>

    <div class="test-container">
      <!-- 基础信息 -->
      <el-card class="info-card">
        <template #header>
          <span>推流信息</span>
        </template>
        <el-form :model="testConfig" label-width="120px">
          <el-form-item label="房间ID:">
            <el-input-number v-model="testConfig.roomId" :min="1" :max="999999" />
          </el-form-item>
          <el-form-item label="用户ID:">
            <el-input v-model="testConfig.userId" placeholder="请输入用户ID" />
          </el-form-item>
          <el-form-item label="推流类型:">
            <el-radio-group v-model="testConfig.streamType">
              <el-radio value="webrtc">WebRTC推流</el-radio>
              <el-radio value="rtmp">RTMP推流</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="generateStreamUrl" :loading="generating">
              生成推流地址
            </el-button>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 推流地址显示 -->
      <el-card v-if="streamInfo.pushUrl" class="url-card">
        <template #header>
          <span>推流地址</span>
        </template>
        <div class="url-info">
          <div class="url-item">
            <label>推流地址:</label>
            <el-input v-model="streamInfo.pushUrl" readonly>
              <template #append>
                <el-button @click="copyToClipboard(streamInfo.pushUrl)">复制</el-button>
              </template>
            </el-input>
          </div>
          <div class="url-item">
            <label>播放地址:</label>
            <el-input v-model="streamInfo.playUrl" readonly>
              <template #append>
                <el-button @click="copyToClipboard(streamInfo.playUrl)">复制</el-button>
              </template>
            </el-input>
          </div>
          <div class="url-item">
            <label>流名称:</label>
            <el-input v-model="streamInfo.streamName" readonly />
          </div>
        </div>
      </el-card>

      <!-- WebRTC推流组件 -->
      <el-card v-if="testConfig.streamType === 'webrtc' && streamInfo.pushUrl" class="pusher-card">
        <template #header>
          <span>WebRTC推流控制</span>
        </template>
        <WebRTCPusher
          :room-id="testConfig.roomId"
          :user-id="testConfig.userId"
          :auto-start="false"
          @push-started="handlePushStarted"
          @push-stopped="handlePushStopped"
          @error="handlePushError"
        />
      </el-card>

      <!-- 传统推流说明 -->
      <el-card v-if="testConfig.streamType === 'rtmp' && streamInfo.pushUrl" class="rtmp-card">
        <template #header>
          <span>RTMP推流说明</span>
        </template>
        <div class="rtmp-guide">
          <el-alert
            title="使用OBS等推流软件"
            description="请将上述推流地址配置到OBS等推流软件中进行推流"
            type="info"
            :closable="false"
            show-icon
          />
          <div class="guide-steps">
            <h4>配置步骤：</h4>
            <ol>
              <li>打开OBS Studio</li>
              <li>在"设置" -> "推流"中选择"自定义"</li>
              <li>将推流地址粘贴到"服务器"字段</li>
              <li>点击"开始推流"</li>
            </ol>
          </div>
        </div>
      </el-card>

      <!-- 推流状态 -->
      <el-card class="status-card">
        <template #header>
          <span>推流状态</span>
        </template>
        <div class="status-info">
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="status-item">
                <span class="label">推流状态:</span>
                <el-tag :type="isStreaming ? 'success' : 'info'">
                  {{ isStreaming ? '推流中' : '未推流' }}
                </el-tag>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <span class="label">推流时长:</span>
                <span class="value">{{ formatDuration(streamDuration) }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="status-item">
                <span class="label">推流类型:</span>
                <span class="value">{{ testConfig.streamType.toUpperCase() }}</span>
              </div>
            </el-col>
          </el-row>
        </div>
      </el-card>

      <!-- 测试日志 -->
      <el-card class="log-card">
        <template #header>
          <div class="log-header">
            <span>测试日志</span>
            <el-button size="small" @click="clearLogs">清空日志</el-button>
          </div>
        </template>
        <div class="log-content">
          <div
            v-for="(log, index) in logs"
            :key="index"
            class="log-item"
            :class="log.type"
          >
            <span class="log-time">{{ log.time }}</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
          <div v-if="logs.length === 0" class="no-logs">
            暂无日志
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import WebRTCPusher from '@/components/WebRTCPusher.vue'
import { liveApi } from '@/services/live'

// 测试配置
const testConfig = ref({
  roomId: 12345,
  userId: 'test_user',
  streamType: 'webrtc' as 'webrtc' | 'rtmp'
})

// 推流信息
const streamInfo = ref({
  pushUrl: '',
  playUrl: '',
  streamName: ''
})

// 状态
const generating = ref(false)
const isStreaming = ref(false)
const streamDuration = ref(0)

// 日志
interface LogItem {
  time: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
}

const logs = ref<LogItem[]>([])

// 定时器
let durationTimer: number | null = null

// 添加日志
const addLog = (message: string, type: LogItem['type'] = 'info') => {
  const now = new Date()
  logs.value.unshift({
    time: now.toLocaleTimeString(),
    message,
    type
  })
  
  // 限制日志数量
  if (logs.value.length > 100) {
    logs.value = logs.value.slice(0, 100)
  }
}

// 生成推流地址
const generateStreamUrl = async () => {
  if (!testConfig.value.userId.trim()) {
    ElMessage.error('请输入用户ID')
    return
  }

  generating.value = true
  addLog('开始生成推流地址...')

  try {
    // 调用后端API生成推流地址
    const response = await fetch(`/api/live/rooms/${testConfig.value.roomId}/start`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      body: JSON.stringify({
        stream_type: testConfig.value.streamType
      })
    })

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`)
    }

    const result = await response.json()
    if (result.code !== 0) {
      throw new Error(result.message || '生成推流地址失败')
    }

    streamInfo.value = {
      pushUrl: result.data.push_url,
      playUrl: result.data.play_url,
      streamName: result.data.stream_name
    }

    addLog(`推流地址生成成功: ${result.data.stream_name}`, 'success')
    ElMessage.success('推流地址生成成功')
  } catch (error: any) {
    console.error('生成推流地址失败:', error)
    addLog(`生成推流地址失败: ${error.message}`, 'error')
    ElMessage.error('生成推流地址失败')
  } finally {
    generating.value = false
  }
}

// 复制到剪贴板
const copyToClipboard = async (text: string) => {
  try {
    await navigator.clipboard.writeText(text)
    ElMessage.success('已复制到剪贴板')
    addLog('地址已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
    addLog('复制到剪贴板失败', 'error')
  }
}

// 推流事件处理
const handlePushStarted = () => {
  isStreaming.value = true
  streamDuration.value = 0
  startDurationTimer()
  addLog('WebRTC推流已开始', 'success')
}

const handlePushStopped = () => {
  isStreaming.value = false
  stopDurationTimer()
  addLog('WebRTC推流已停止', 'warning')
}

const handlePushError = (error: Error) => {
  addLog(`推流错误: ${error.message}`, 'error')
}

// 开始计时
const startDurationTimer = () => {
  durationTimer = window.setInterval(() => {
    streamDuration.value++
  }, 1000)
}

// 停止计时
const stopDurationTimer = () => {
  if (durationTimer) {
    clearInterval(durationTimer)
    durationTimer = null
  }
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (hours > 0) {
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }
  return `${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 清空日志
const clearLogs = () => {
  logs.value = []
  addLog('日志已清空')
}

onMounted(() => {
  addLog('Web推流测试页面已加载')
})

onUnmounted(() => {
  stopDurationTimer()
})
</script>

<style scoped>
.web-push-test {
  min-height: 100vh;
  background: #f5f7fa;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0 0 10px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
}

.test-container {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.url-item {
  margin-bottom: 15px;
}

.url-item:last-child {
  margin-bottom: 0;
}

.url-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
  color: #303133;
}

.guide-steps {
  margin-top: 15px;
}

.guide-steps h4 {
  margin: 0 0 10px 0;
  color: #303133;
}

.guide-steps ol {
  margin: 0;
  padding-left: 20px;
}

.guide-steps li {
  margin-bottom: 5px;
  color: #606266;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-item .label {
  font-weight: 500;
  color: #606266;
}

.status-item .value {
  color: #303133;
}

.log-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.log-content {
  max-height: 300px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 5px 0;
  border-bottom: 1px solid #ebeef5;
  font-size: 14px;
}

.log-item:last-child {
  border-bottom: none;
}

.log-time {
  color: #909399;
  white-space: nowrap;
}

.log-message {
  flex: 1;
}

.log-item.info .log-message {
  color: #606266;
}

.log-item.success .log-message {
  color: #67c23a;
}

.log-item.warning .log-message {
  color: #e6a23c;
}

.log-item.error .log-message {
  color: #f56c6c;
}

.no-logs {
  text-align: center;
  color: #909399;
  padding: 20px;
}
</style>
