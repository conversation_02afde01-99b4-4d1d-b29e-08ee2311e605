<template>
  <div class="admin-dashboard">
    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon user-icon">
          <el-icon size="24"><User /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalUsers }}</div>
          <div class="stat-label">总用户数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon room-icon">
          <el-icon size="24"><VideoPlay /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.totalRooms }}</div>
          <div class="stat-label">直播间数</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon live-icon">
          <el-icon size="24"><VideoCamera /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">{{ stats.liveRooms }}</div>
          <div class="stat-label">正在直播</div>
        </div>
      </div>
      
      <div class="stat-card">
        <div class="stat-icon revenue-icon">
          <el-icon size="24"><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-value">¥{{ stats.totalRevenue }}</div>
          <div class="stat-label">总收入</div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-row">
        <div class="chart-card">
          <div class="chart-header">
            <h3>用户增长趋势</h3>
          </div>
          <div class="chart-content">
            <div id="userChart" style="height: 300px;"></div>
          </div>
        </div>
        
        <div class="chart-card">
          <div class="chart-header">
            <h3>直播间统计</h3>
          </div>
          <div class="chart-content">
            <div id="roomChart" style="height: 300px;"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 最新数据 -->
    <div class="recent-section">
      <div class="recent-card">
        <div class="card-header">
          <h3>最新注册用户</h3>
          <el-button type="text" @click="$router.push('/admin/users')">查看更多</el-button>
        </div>
        <div class="card-content">
          <el-table :data="recentUsers" style="width: 100%">
            <el-table-column label="头像" width="60">
              <template #default="{ row }">
                <el-avatar :src="row.avatar" :size="32">
                  {{ row.nickname?.charAt(0) }}
                </el-avatar>
              </template>
            </el-table-column>
            <el-table-column prop="nickname" label="昵称" />
            <el-table-column prop="email" label="邮箱" />
            <el-table-column prop="created_at" label="注册时间" width="180">
              <template #default="{ row }">
                {{ formatDate(row.created_at) }}
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      
      <div class="recent-card">
        <div class="card-header">
          <h3>热门直播间</h3>
          <el-button type="text" @click="$router.push('/admin/rooms')">查看更多</el-button>
        </div>
        <div class="card-content">
          <el-table :data="hotRooms" style="width: 100%">
            <el-table-column prop="title" label="标题" />
            <el-table-column prop="user.nickname" label="主播" width="120" />
            <el-table-column prop="viewer_count" label="观看人数" width="100">
              <template #default="{ row }">
                {{ formatNumber(row.viewer_count) }}
              </template>
            </el-table-column>
            <el-table-column label="状态" width="80">
              <template #default="{ row }">
                <el-tag :type="row.status === 1 ? 'success' : 'info'">
                  {{ row.status === 1 ? '直播中' : '未开播' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

const stats = ref({
  totalUsers: 0,
  totalRooms: 0,
  liveRooms: 0,
  totalRevenue: 0
})

const recentUsers = ref([])
const hotRooms = ref([])

// 获取统计数据
const getStats = async () => {
  try {
    // TODO: 调用API获取统计数据
    stats.value = {
      totalUsers: 1234,
      totalRooms: 567,
      liveRooms: 89,
      totalRevenue: 12345.67
    }
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

// 获取最新用户
const getRecentUsers = async () => {
  try {
    // TODO: 调用API获取最新用户
    recentUsers.value = [
      {
        id: 1,
        nickname: '新用户1',
        email: '<EMAIL>',
        avatar: '',
        created_at: '2024-01-01 12:00:00'
      }
    ]
  } catch (error) {
    console.error('获取最新用户失败:', error)
  }
}

// 获取热门直播间
const getHotRooms = async () => {
  try {
    // TODO: 调用API获取热门直播间
    hotRooms.value = [
      {
        id: 1,
        title: '热门直播间1',
        user: { nickname: '主播1' },
        viewer_count: 1234,
        status: 1
      }
    ]
  } catch (error) {
    console.error('获取热门直播间失败:', error)
  }
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  getStats()
  getRecentUsers()
  getHotRooms()
})
</script>

<style scoped>
.admin-dashboard {
  padding: 20px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  color: #fff;
}

.user-icon {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.room-icon {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.live-icon {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.revenue-icon {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #909399;
}

.charts-section {
  margin-bottom: 30px;
}

.chart-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chart-header {
  margin-bottom: 20px;
}

.chart-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

.recent-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.recent-card {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.card-header h3 {
  margin: 0;
  color: #303133;
  font-size: 16px;
}

@media (max-width: 768px) {
  .chart-row,
  .recent-section {
    grid-template-columns: 1fr;
  }
}
</style>
