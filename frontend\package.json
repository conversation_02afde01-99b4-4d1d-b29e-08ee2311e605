{"name": "live-platform-frontend", "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "type-check": "vue-tsc --noEmit"}, "dependencies": {"vue": "^3.4.31", "vue-router": "^4.4.0", "pinia": "^2.1.7", "element-plus": "^2.7.6", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.2", "@tencentcloud/chat": "^3.2.7", "tim-js-sdk": "^2.27.6", "tim-upload-plugin": "^1.4.2", "tcplayer.js": "^5.3.4", "trtc-js-sdk": "^4.15.8", "dayjs": "^1.11.11", "lodash-es": "^4.17.21", "nprogress": "^0.2.0"}, "devDependencies": {"@types/node": "^20.14.9", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@typescript-eslint/eslint-plugin": "^7.15.0", "@typescript-eslint/parser": "^7.15.0", "@vitejs/plugin-vue": "^5.0.5", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.27.0", "prettier": "^3.3.2", "sass": "^1.77.6", "typescript": "~5.5.3", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "vite": "^5.3.3", "vue-tsc": "^2.0.24"}}