// 腾讯云即时通信 IM SDK 封装
import TencentCloudChat from '@tencentcloud/chat'
import TIMUploadPlugin from 'tim-upload-plugin'

// 兼容旧版本 tim-js-sdk
let TIM: any
try {
  // 优先使用新版本 @tencentcloud/chat
  TIM = TencentCloudChat
} catch (error) {
  // 如果新版本不可用，回退到旧版本
  try {
    const timJsSDK = require('tim-js-sdk')
    TIM = timJsSDK.default || timJsSDK
  } catch (fallbackError) {
    console.error('无法加载腾讯云 IM SDK:', fallbackError)
  }
}

export interface ChatConfig {
  SDKAppID: number
  userID: string
  userSig: string
}

export interface ChatMessage {
  ID: string
  type: string
  payload: any
  from: string
  to: string
  time: number
  conversationType: string
}

export class ChatManager {
  private chat: any = null
  private isReady = false
  private eventListeners: Map<string, Function[]> = new Map()

  constructor() {
    this.initEventListeners()
  }

  // 初始化 IM SDK
  async init(config: ChatConfig): Promise<void> {
    try {
      if (!TIM) {
        throw new Error('腾讯云 IM SDK 未正确加载')
      }

      // 创建 SDK 实例
      this.chat = TIM.create({
        SDKAppID: config.SDKAppID
      })

      // 设置日志级别
      this.chat.setLogLevel(0) // 开发环境使用详细日志

      // 注册上传插件
      this.chat.registerPlugin({
        'tim-upload-plugin': TIMUploadPlugin
      })

      // 监听 SDK 事件
      this.setupEventListeners()

      // 登录
      await this.login(config.userID, config.userSig)

    } catch (error) {
      console.error('IM SDK 初始化失败:', error)
      throw error
    }
  }

  // 登录
  private async login(userID: string, userSig: string): Promise<void> {
    try {
      const response = await this.chat.login({
        userID,
        userSig
      })

      console.log('IM 登录成功:', response.data)
      
      if (response.data.repeatLogin) {
        console.log('重复登录:', response.data.errorInfo)
      }
    } catch (error) {
      console.error('IM 登录失败:', error)
      throw error
    }
  }

  // 设置事件监听
  private setupEventListeners(): void {
    if (!this.chat) return

    // SDK 准备就绪
    this.chat.on(TIM.EVENT.SDK_READY, () => {
      console.log('IM SDK 准备就绪')
      this.isReady = true
      this.emit('ready')
    })

    // 收到新消息
    this.chat.on(TIM.EVENT.MESSAGE_RECEIVED, (event: any) => {
      console.log('收到新消息:', event.data)
      this.emit('messageReceived', event.data)
    })

    // 消息已读回执
    this.chat.on(TIM.EVENT.MESSAGE_READ_BY_PEER, (event: any) => {
      console.log('消息已读:', event.data)
      this.emit('messageRead', event.data)
    })

    // 会话列表更新
    this.chat.on(TIM.EVENT.CONVERSATION_LIST_UPDATED, (event: any) => {
      console.log('会话列表更新:', event.data)
      this.emit('conversationListUpdated', event.data)
    })

    // 群组列表更新
    this.chat.on(TIM.EVENT.GROUP_LIST_UPDATED, (event: any) => {
      console.log('群组列表更新:', event.data)
      this.emit('groupListUpdated', event.data)
    })

    // 网络状态变化
    this.chat.on(TIM.EVENT.NET_STATE_CHANGE, (event: any) => {
      console.log('网络状态变化:', event.data)
      this.emit('netStateChange', event.data)
    })

    // 被踢下线
    this.chat.on(TIM.EVENT.KICKED_OUT, (event: any) => {
      console.log('被踢下线:', event.data)
      this.emit('kickedOut', event.data)
    })

    // SDK 错误
    this.chat.on(TIM.EVENT.ERROR, (event: any) => {
      console.error('IM SDK 错误:', event.data)
      this.emit('error', event.data)
    })
  }

  // 发送文本消息
  async sendTextMessage(to: string, text: string, conversationType: string = TIM.TYPES.CONV_C2C): Promise<any> {
    if (!this.isReady) {
      throw new Error('IM SDK 未准备就绪')
    }

    try {
      const message = this.chat.createTextMessage({
        to,
        conversationType,
        payload: {
          text
        }
      })

      const response = await this.chat.sendMessage(message)
      console.log('文本消息发送成功:', response)
      return response
    } catch (error) {
      console.error('文本消息发送失败:', error)
      throw error
    }
  }

  // 发送图片消息
  async sendImageMessage(to: string, file: File, conversationType: string = TIM.TYPES.CONV_C2C): Promise<any> {
    if (!this.isReady) {
      throw new Error('IM SDK 未准备就绪')
    }

    try {
      const message = this.chat.createImageMessage({
        to,
        conversationType,
        payload: {
          file
        }
      })

      const response = await this.chat.sendMessage(message)
      console.log('图片消息发送成功:', response)
      return response
    } catch (error) {
      console.error('图片消息发送失败:', error)
      throw error
    }
  }

  // 发送自定义消息
  async sendCustomMessage(to: string, data: any, conversationType: string = TIM.TYPES.CONV_C2C): Promise<any> {
    if (!this.isReady) {
      throw new Error('IM SDK 未准备就绪')
    }

    try {
      const message = this.chat.createCustomMessage({
        to,
        conversationType,
        payload: {
          data: JSON.stringify(data),
          description: data.description || '',
          extension: data.extension || ''
        }
      })

      const response = await this.chat.sendMessage(message)
      console.log('自定义消息发送成功:', response)
      return response
    } catch (error) {
      console.error('自定义消息发送失败:', error)
      throw error
    }
  }

  // 获取会话列表
  async getConversationList(): Promise<any> {
    if (!this.isReady) {
      throw new Error('IM SDK 未准备就绪')
    }

    try {
      const response = await this.chat.getConversationList()
      console.log('获取会话列表成功:', response.data)
      return response.data
    } catch (error) {
      console.error('获取会话列表失败:', error)
      throw error
    }
  }

  // 获取消息列表
  async getMessageList(conversationID: string, nextReqMessageID?: string): Promise<any> {
    if (!this.isReady) {
      throw new Error('IM SDK 未准备就绪')
    }

    try {
      const response = await this.chat.getMessageList({
        conversationID,
        nextReqMessageID,
        count: 15
      })
      console.log('获取消息列表成功:', response.data)
      return response.data
    } catch (error) {
      console.error('获取消息列表失败:', error)
      throw error
    }
  }

  // 创建群组
  async createGroup(options: {
    type: string
    name: string
    memberList?: any[]
    introduction?: string
    notification?: string
    avatar?: string
  }): Promise<any> {
    if (!this.isReady) {
      throw new Error('IM SDK 未准备就绪')
    }

    try {
      const response = await this.chat.createGroup({
        type: options.type || TIM.TYPES.GRP_CHATROOM, // 聊天室类型
        name: options.name,
        memberList: options.memberList || [],
        introduction: options.introduction || '',
        notification: options.notification || '',
        avatar: options.avatar || ''
      })
      console.log('创建群组成功:', response.data)
      return response.data
    } catch (error) {
      console.error('创建群组失败:', error)
      throw error
    }
  }

  // 加入群组
  async joinGroup(groupID: string): Promise<any> {
    if (!this.isReady) {
      throw new Error('IM SDK 未准备就绪')
    }

    try {
      const response = await this.chat.joinGroup({
        groupID,
        type: TIM.TYPES.GRP_CHATROOM
      })
      console.log('加入群组成功:', response.data)
      return response.data
    } catch (error) {
      console.error('加入群组失败:', error)
      throw error
    }
  }

  // 退出群组
  async quitGroup(groupID: string): Promise<any> {
    if (!this.isReady) {
      throw new Error('IM SDK 未准备就绪')
    }

    try {
      const response = await this.chat.quitGroup(groupID)
      console.log('退出群组成功:', response.data)
      return response.data
    } catch (error) {
      console.error('退出群组失败:', error)
      throw error
    }
  }

  // 登出
  async logout(): Promise<void> {
    if (!this.chat) return

    try {
      await this.chat.logout()
      console.log('IM 登出成功')
      this.isReady = false
    } catch (error) {
      console.error('IM 登出失败:', error)
      throw error
    }
  }

  // 销毁实例
  destroy(): void {
    if (this.chat) {
      this.chat.destroy()
      this.chat = null
      this.isReady = false
      this.eventListeners.clear()
    }
  }

  // 事件监听器管理
  private initEventListeners(): void {
    this.eventListeners = new Map()
  }

  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, [])
    }
    this.eventListeners.get(event)!.push(callback)
  }

  off(event: string, callback?: Function): void {
    if (!this.eventListeners.has(event)) return

    if (callback) {
      const callbacks = this.eventListeners.get(event)!
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    } else {
      this.eventListeners.delete(event)
    }
  }

  private emit(event: string, data?: any): void {
    if (!this.eventListeners.has(event)) return

    const callbacks = this.eventListeners.get(event)!
    callbacks.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error(`事件回调执行失败 [${event}]:`, error)
      }
    })
  }

  // 获取 SDK 实例
  getSDK(): any {
    return this.chat
  }

  // 获取 TIM 常量
  static get TIM(): any {
    return TIM
  }

  // 检查是否准备就绪
  get ready(): boolean {
    return this.isReady
  }
}

// 导出单例实例
export const chatManager = new ChatManager()

// 导出 TIM 常量
export { TIM }

// 简化的聊天功能接口，用于直播间
let currentRoomId: string = ''
let messageCallbacks: Function[] = []

// 初始化聊天（简化版本）
export const initChat = (roomId: string) => {
  currentRoomId = roomId
  console.log('初始化聊天室:', roomId)
  // TODO: 实际项目中这里应该初始化 IM SDK 并加入群组
}

// 发送聊天消息（简化版本）
export const sendChatMessage = (message: any) => {
  console.log('发送消息:', message)
  // TODO: 实际项目中这里应该调用 IM SDK 发送消息

  // 模拟消息发送成功，触发回调
  setTimeout(() => {
    messageCallbacks.forEach(callback => {
      callback({
        id: Date.now(),
        type: message.type,
        content: message.content,
        user: message.user,
        gift: message.gift,
        timestamp: Date.now()
      })
    })
  }, 100)
}

// 监听聊天消息（简化版本）
export const onChatMessage = (callback: Function) => {
  messageCallbacks.push(callback)
  console.log('添加消息监听器')
}

// 清理聊天监听器
export const offChatMessage = (callback: Function) => {
  const index = messageCallbacks.indexOf(callback)
  if (index > -1) {
    messageCallbacks.splice(index, 1)
  }
}
