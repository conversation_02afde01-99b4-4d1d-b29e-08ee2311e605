<template>
  <div class="admin-rooms">
    <div class="page-header">
      <h2>直播间管理</h2>
      <div class="header-actions">
        <el-select v-model="statusFilter" placeholder="状态筛选" style="width: 120px">
          <el-option label="全部" value="" />
          <el-option label="未开播" :value="0" />
          <el-option label="直播中" :value="1" />
          <el-option label="暂停" :value="2" />
          <el-option label="已结束" :value="3" />
        </el-select>
        <el-input
          v-model="searchKeyword"
          placeholder="搜索直播间..."
          style="width: 300px"
          @keyup.enter="handleSearch"
        >
          <template #append>
            <el-button @click="handleSearch">
              <el-icon><Search /></el-icon>
            </el-button>
          </template>
        </el-input>
      </div>
    </div>

    <div class="page-content">
      <el-table
        v-loading="loading"
        :data="rooms"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="封面" width="120">
          <template #default="{ row }">
            <el-image
              :src="row.cover_image"
              style="width: 80px; height: 60px"
              fit="cover"
              :preview-src-list="[row.cover_image]"
            />
          </template>
        </el-table-column>
        <el-table-column prop="title" label="标题" min-width="200" />
        <el-table-column label="主播" width="120">
          <template #default="{ row }">
            <div class="streamer-info">
              <el-avatar :src="row.user?.avatar" :size="32">
                {{ row.user?.nickname?.charAt(0) }}
              </el-avatar>
              <span>{{ row.user?.nickname }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="分类" width="100">
          <template #default="{ row }">
            <el-tag v-if="row.category" size="small">
              {{ row.category.name }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="viewer_count" label="观看人数" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.viewer_count) }}
          </template>
        </el-table-column>
        <el-table-column prop="like_count" label="点赞数" width="100">
          <template #default="{ row }">
            {{ formatNumber(row.like_count) }}
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleView(row)"
            >
              查看
            </el-button>
            <el-button
              type="warning"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 编辑直播间对话框 -->
    <el-dialog
      v-model="editDialogVisible"
      title="编辑直播间"
      width="600px"
    >
      <el-form
        ref="editFormRef"
        :model="editForm"
        :rules="editRules"
        label-width="80px"
      >
        <el-form-item label="标题" prop="title">
          <el-input v-model="editForm.title" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="editForm.description"
            type="textarea"
            :rows="3"
          />
        </el-form-item>
        <el-form-item label="分类" prop="category_id">
          <el-select v-model="editForm.category_id" placeholder="请选择分类">
            <el-option
              v-for="category in categories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="editForm.status">
            <el-radio :label="0">未开播</el-radio>
            <el-radio :label="1">直播中</el-radio>
            <el-radio :label="2">暂停</el-radio>
            <el-radio :label="3">已结束</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveEdit">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useRouter } from 'vue-router'

const router = useRouter()

const loading = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const rooms = ref([])
const categories = ref([])
const currentPage = ref(1)
const pageSize = ref(20)
const total = ref(0)

const editDialogVisible = ref(false)
const editFormRef = ref()
const editForm = ref({
  id: 0,
  title: '',
  description: '',
  category_id: null,
  status: 0
})

const editRules = {
  title: [
    { required: true, message: '请输入标题', trigger: 'blur' }
  ],
  category_id: [
    { required: true, message: '请选择分类', trigger: 'change' }
  ]
}

// 获取直播间列表
const getRoomList = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取直播间列表
    rooms.value = [
      {
        id: 1,
        title: '测试直播间',
        description: '这是一个测试直播间',
        cover_image: '/images/default-cover.jpg',
        user: {
          id: 1,
          nickname: '主播1',
          avatar: ''
        },
        category: {
          id: 1,
          name: '游戏'
        },
        status: 1,
        viewer_count: 1234,
        like_count: 567,
        created_at: '2024-01-01 12:00:00'
      }
    ]
    total.value = 1
  } catch (error) {
    console.error('获取直播间列表失败:', error)
    ElMessage.error('获取直播间列表失败')
  } finally {
    loading.value = false
  }
}

// 获取分类列表
const getCategories = async () => {
  try {
    // TODO: 调用API获取分类列表
    categories.value = [
      { id: 1, name: '游戏' },
      { id: 2, name: '音乐' },
      { id: 3, name: '聊天' }
    ]
  } catch (error) {
    console.error('获取分类列表失败:', error)
  }
}

// 搜索
const handleSearch = () => {
  currentPage.value = 1
  getRoomList()
}

// 查看直播间
const handleView = (room: any) => {
  router.push(`/live/${room.id}`)
}

// 编辑直播间
const handleEdit = (room: any) => {
  editForm.value = {
    id: room.id,
    title: room.title,
    description: room.description,
    category_id: room.category?.id,
    status: room.status
  }
  editDialogVisible.value = true
}

// 保存编辑
const handleSaveEdit = async () => {
  try {
    await editFormRef.value.validate()
    // TODO: 调用API保存直播间信息
    ElMessage.success('保存成功')
    editDialogVisible.value = false
    getRoomList()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 删除直播间
const handleDelete = async (room: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除直播间 ${room.title} 吗？此操作不可恢复！`, '确认删除', {
      type: 'warning'
    })
    // TODO: 调用API删除直播间
    ElMessage.success('删除成功')
    getRoomList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 分页变化
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  getRoomList()
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
  getRoomList()
}

// 获取状态类型
const getStatusType = (status: number) => {
  const typeMap = {
    0: 'info',
    1: 'success',
    2: 'warning',
    3: 'danger'
  }
  return typeMap[status as keyof typeof typeMap] || 'info'
}

// 获取状态文本
const getStatusText = (status: number) => {
  const statusMap = {
    0: '未开播',
    1: '直播中',
    2: '暂停',
    3: '已结束'
  }
  return statusMap[status as keyof typeof statusMap] || '未知'
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  getRoomList()
  getCategories()
})
</script>

<style scoped>
.admin-rooms {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.page-content {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
