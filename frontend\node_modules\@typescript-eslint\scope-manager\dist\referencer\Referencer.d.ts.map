{"version": 3, "file": "Referencer.d.ts", "sourceRoot": "", "sources": ["../../src/referencer/Referencer.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,GAAG,EAAE,QAAQ,EAAE,MAAM,0BAA0B,CAAC;AAe9D,OAAO,KAAK,EAAe,KAAK,EAAE,MAAM,UAAU,CAAC;AACnD,OAAO,KAAK,EAAE,YAAY,EAAE,MAAM,iBAAiB,CAAC;AAKpD,OAAO,KAAK,EAAE,uBAAuB,EAAE,MAAM,aAAa,CAAC;AAG3D,OAAO,KAAK,EAAE,cAAc,EAAE,MAAM,WAAW,CAAC;AAChD,OAAO,EAAE,OAAO,EAAE,MAAM,WAAW,CAAC;AAEpC,UAAU,iBAAkB,SAAQ,cAAc;IAChD,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IACzB,eAAe,EAAE,MAAM,GAAG,IAAI,CAAC;IAC/B,GAAG,EAAE,GAAG,EAAE,CAAC;CACZ;AAGD,cAAM,UAAW,SAAQ,OAAO;;IAM9B,SAAgB,YAAY,EAAE,YAAY,CAAC;gBAE/B,OAAO,EAAE,iBAAiB,EAAE,YAAY,EAAE,YAAY;IAQ3D,YAAY,IAAI,KAAK;IACrB,YAAY,CAAC,WAAW,EAAE,IAAI,GAAG,KAAK,GAAG,IAAI;IAQ7C,KAAK,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI;IAQhC,uBAAuB,CAC5B,OAAO,EAAE,QAAQ,CAAC,UAAU,EAC5B,WAAW,EAAE,CAAC,QAAQ,CAAC,oBAAoB,GAAG,QAAQ,CAAC,iBAAiB,CAAC,EAAE,EAC3E,mBAAmB,EAAE,uBAAuB,GAAG,IAAI,EACnD,IAAI,EAAE,OAAO,GACZ,IAAI;IAYP,OAAO,CAAC,sBAAsB;IAoB9B;;OAEG;IACH,OAAO,CAAC,yBAAyB;IAgBjC,OAAO,CAAC,kBAAkB;IAS1B,OAAO,CAAC,oBAAoB;IAgB5B,SAAS,CAAC,UAAU,CAClB,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,QAAQ,CAAC,eAAe,GACzD,IAAI;IAIP,SAAS,CAAC,UAAU,CAClB,IAAI,EAAE,QAAQ,CAAC,cAAc,GAAG,QAAQ,CAAC,cAAc,GACtD,IAAI;IAoDP,SAAS,CAAC,oCAAoC,CAC5C,IAAI,EAAE,QAAQ,CAAC,SAAS,GACvB,IAAI;IAaP,SAAS,CAAC,aAAa,CACrB,IAAI,EACA,QAAQ,CAAC,uBAAuB,GAChC,QAAQ,CAAC,mBAAmB,GAC5B,QAAQ,CAAC,kBAAkB,GAC3B,QAAQ,CAAC,iBAAiB,GAC1B,QAAQ,CAAC,6BAA6B,GACzC,IAAI;IA2DP,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI;IAQtD,SAAS,CAAC,SAAS,CAAC,IAAI,EAAE,QAAQ,CAAC,IAAI,GAAG,IAAI,GAAG,SAAS,GAAG,IAAI;IAOjE,SAAS,CAAC,kBAAkB,CAC1B,IAAI,EACA,QAAQ,CAAC,cAAc,GACvB,QAAQ,CAAC,qBAAqB,GAC9B,QAAQ,CAAC,eAAe,GAC3B,IAAI;IASP,SAAS,CAAC,uBAAuB,CAC/B,IAAI,EAAE,QAAQ,CAAC,uBAAuB,GACrC,IAAI;IAIP,SAAS,CAAC,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,oBAAoB,GAAG,IAAI;IAqDzE,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,GAAG,IAAI;IAQ7D,SAAS,CAAC,cAAc,IAAI,IAAI;IAIhC,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,GAAG,IAAI;IAK7D,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,GAAG,IAAI;IAsBvD,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,eAAe,GAAG,IAAI;IAI/D,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAIjE,SAAS,CAAC,iBAAiB,IAAI,IAAI;IAInC,SAAS,CAAC,oBAAoB,IAAI,IAAI;IAItC,SAAS,CAAC,wBAAwB,CAChC,IAAI,EAAE,QAAQ,CAAC,wBAAwB,GACtC,IAAI;IAQP,SAAS,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,kBAAkB,GAAG,IAAI;IAarE,SAAS,CAAC,sBAAsB,CAC9B,IAAI,EAAE,QAAQ,CAAC,sBAAsB,GACpC,IAAI;IAQP,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,GAAG,IAAI;IAI7D,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,GAAG,IAAI;IAI7D,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,GAAG,IAAI;IAiBzD,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,mBAAmB,GAAG,IAAI;IAIvE,SAAS,CAAC,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,kBAAkB,GAAG,IAAI;IAIrE,SAAS,CAAC,UAAU,CAAC,IAAI,EAAE,QAAQ,CAAC,UAAU,GAAG,IAAI;IAKrD,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,iBAAiB,GAAG,IAAI;IASnE,SAAS,CAAC,YAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,YAAY,GAAG,IAAI;IAIzD,SAAS,CAAC,iBAAiB,IAAI,IAAI;IAInC,SAAS,CAAC,WAAW,CAAC,IAAI,EAAE,QAAQ,CAAC,WAAW,GAAG,IAAI;IAMvD,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,aAAa,GAAG,IAAI;IAI3D,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,mBAAmB,GAAG,IAAI;IAQvE,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,iBAAiB,GAAG,IAAI;IAuBnE,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAIjE,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAOjE,SAAS,CAAC,YAAY,IAAI,IAAI;IAI9B,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,aAAa,GAAG,IAAI;IAK3D,SAAS,CAAC,iBAAiB,IAAI,IAAI;IAInC,SAAS,CAAC,OAAO,CAAC,IAAI,EAAE,QAAQ,CAAC,OAAO,GAAG,IAAI;IAsB/C,SAAS,CAAC,QAAQ,CAAC,IAAI,EAAE,QAAQ,CAAC,QAAQ,GAAG,IAAI;IAIjD,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,eAAe,GAAG,IAAI;IAY/D,SAAS,CAAC,wBAAwB,CAChC,IAAI,EAAE,QAAQ,CAAC,wBAAwB,GACtC,IAAI;IAMP,SAAS,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,CAAC,cAAc,GAAG,IAAI;IAI7D,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,iBAAiB,GAAG,IAAI;IAInE,SAAS,CAAC,yBAAyB,CACjC,IAAI,EAAE,QAAQ,CAAC,yBAAyB,GACvC,IAAI;IAaP,SAAS,CAAC,6BAA6B,CACrC,IAAI,EAAE,QAAQ,CAAC,6BAA6B,GAC3C,IAAI;IAIP,SAAS,CAAC,iBAAiB,CAAC,IAAI,EAAE,QAAQ,CAAC,iBAAiB,GAAG,IAAI;IA+CnE,SAAS,CAAC,yBAAyB,CACjC,IAAI,EAAE,QAAQ,CAAC,yBAAyB,GACvC,IAAI;IAKP,SAAS,CAAC,sBAAsB,CAC9B,IAAI,EAAE,QAAQ,CAAC,sBAAsB,GACpC,IAAI;IAIP,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,mBAAmB,GAAG,IAAI;IAevE,SAAS,CAAC,qBAAqB,CAAC,IAAI,EAAE,QAAQ,CAAC,qBAAqB,GAAG,IAAI;IAI3E,SAAS,CAAC,sBAAsB,CAC9B,IAAI,EAAE,QAAQ,CAAC,sBAAsB,GACpC,IAAI;IAIP,SAAS,CAAC,eAAe,CAAC,IAAI,EAAE,QAAQ,CAAC,eAAe,GAAG,IAAI;IAI/D,SAAS,CAAC,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,gBAAgB,GAAG,IAAI;IAcjE,SAAS,CAAC,mBAAmB,CAAC,IAAI,EAAE,QAAQ,CAAC,mBAAmB,GAAG,IAAI;IAoCvE,SAAS,CAAC,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,aAAa,GAAG,IAAI;IAW3D,SAAS,CAAC,eAAe,IAAI,IAAI;CAGlC;AAED,OAAO,EAAE,UAAU,EAAE,iBAAiB,EAAE,CAAC"}