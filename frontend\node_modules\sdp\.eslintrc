{
  "rules": {
    "array-bracket-spacing": 2,
    "block-spacing": [2, "never"],
    "brace-style": [2, "1tbs", {"allowSingleLine": false}],
    "camelcase": [2, {"properties": "always"}],
    "curly": 2,
    "default-case": 2,
    "dot-notation": 2,
    "eqeqeq": 2,
    "indent": [
        2,
        2,
        {"SwitchCase": 1}
    ],
    "key-spacing": [2, {"beforeColon": false, "afterColon": true}],
    "max-len": [2, 80, 2, {"ignoreUrls": true}],
    "new-cap": [2, {"newIsCapExceptions": [
        "webkitRTCPeerConnection",
        "mozRTCPeerConnection"
    ]}],
    "no-console": 0,
    "no-else-return": 2,
    "no-eval": 2,
    "no-multi-spaces": 2,
    "no-multiple-empty-lines": [2, {"max": 2}],
    "no-shadow": 2,
    "no-trailing-spaces": 2,
    "no-unused-expressions": 2,
    "no-unused-vars": [2, {"args": "none"}],
    "object-curly-spacing": [2, "never"],
    "padded-blocks": [2, "never"],
    "quotes": [
        2,
        "single"
    ],
    "semi": [
        2,
        "always"
    ],
    "keyword-spacing": 2,
    "space-before-blocks": 2,
    "space-before-function-paren": [2, "never"],
    "space-unary-ops": 2,
    "space-infix-ops": 2,
    "spaced-comment": 2,
    "valid-typeof": 2
  },
  "env": {
      "es6": true,
      "browser": true,
      "node": true
  },
  "extends": ["eslint:recommended"],
  "globals": {
    "module": true,
    "require": true,
    "process": true,
    "Promise": true,
  }
}
