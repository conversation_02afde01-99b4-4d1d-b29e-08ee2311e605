<template>
  <div class="admin-gifts">
    <div class="page-header">
      <h2>礼物管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          添加礼物
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <el-table
        v-loading="loading"
        :data="gifts"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="图标" width="80">
          <template #default="{ row }">
            <el-image
              :src="row.icon"
              style="width: 40px; height: 40px"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column prop="name" label="礼物名称" />
        <el-table-column prop="price" label="价格" width="100">
          <template #default="{ row }">
            ¥{{ row.price }}
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleToggleStatus(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑礼物对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑礼物' : '添加礼物'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="礼物名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入礼物名称" />
        </el-form-item>
        <el-form-item label="价格" prop="price">
          <el-input-number
            v-model="form.price"
            :min="0"
            :precision="2"
            placeholder="请输入价格"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入礼物描述"
          />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-upload
            class="gift-uploader"
            action="/api/upload"
            :show-file-list="false"
            :on-success="handleUploadSuccess"
            :before-upload="beforeUpload"
          >
            <el-image
              v-if="form.icon"
              :src="form.icon"
              style="width: 80px; height: 80px"
              fit="cover"
            />
            <el-icon v-else class="gift-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="form.sort_order"
            :min="0"
            :max="999"
            placeholder="排序值"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const gifts = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

const form = ref({
  id: 0,
  name: '',
  price: 0,
  description: '',
  icon: '',
  sort_order: 0,
  status: 1
})

const rules = {
  name: [
    { required: true, message: '请输入礼物名称', trigger: 'blur' }
  ],
  price: [
    { required: true, message: '请输入价格', trigger: 'blur' }
  ],
  icon: [
    { required: true, message: '请上传礼物图标', trigger: 'change' }
  ]
}

// 获取礼物列表
const getGiftList = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取礼物列表
    gifts.value = [
      {
        id: 1,
        name: '玫瑰花',
        price: 1.00,
        description: '送给心爱的人',
        icon: '/images/gifts/rose.png',
        sort_order: 1,
        status: 1,
        created_at: '2024-01-01 12:00:00'
      },
      {
        id: 2,
        name: '跑车',
        price: 100.00,
        description: '豪华跑车',
        icon: '/images/gifts/car.png',
        sort_order: 2,
        status: 1,
        created_at: '2024-01-01 12:00:00'
      }
    ]
  } catch (error) {
    console.error('获取礼物列表失败:', error)
    ElMessage.error('获取礼物列表失败')
  } finally {
    loading.value = false
  }
}

// 添加礼物
const handleAdd = () => {
  isEdit.value = false
  form.value = {
    id: 0,
    name: '',
    price: 0,
    description: '',
    icon: '',
    sort_order: 0,
    status: 1
  }
  dialogVisible.value = true
}

// 编辑礼物
const handleEdit = (gift: any) => {
  isEdit.value = true
  form.value = {
    id: gift.id,
    name: gift.name,
    price: gift.price,
    description: gift.description,
    icon: gift.icon,
    sort_order: gift.sort_order,
    status: gift.status
  }
  dialogVisible.value = true
}

// 保存礼物
const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      // TODO: 调用API更新礼物
      ElMessage.success('更新成功')
    } else {
      // TODO: 调用API添加礼物
      ElMessage.success('添加成功')
    }
    
    dialogVisible.value = false
    getGiftList()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 切换状态
const handleToggleStatus = async (gift: any) => {
  try {
    // TODO: 调用API切换状态
    ElMessage.success('状态更新成功')
    getGiftList()
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    gift.status = gift.status === 1 ? 0 : 1
  }
}

// 删除礼物
const handleDelete = async (gift: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除礼物 ${gift.name} 吗？此操作不可恢复！`, '确认删除', {
      type: 'warning'
    })
    
    // TODO: 调用API删除礼物
    ElMessage.success('删除成功')
    getGiftList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 上传成功回调
const handleUploadSuccess = (response: any) => {
  form.value.icon = response.url
  ElMessage.success('上传成功')
}

// 上传前检查
const beforeUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  getGiftList()
})
</script>

<style scoped>
.admin-gifts {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-content {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.gift-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.gift-uploader:hover {
  border-color: #409eff;
}

.gift-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}
</style>
