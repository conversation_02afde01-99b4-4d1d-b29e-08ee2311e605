import axios from 'axios'

// TRTC 配置接口
export interface TRTCConfig {
  sdkAppId: number
  userSig: string
  userId: string
}

// TRTC 配置服务
export class TRTCConfigService {
  private static instance: TRTCConfigService
  private config: TRTCConfig | null = null

  private constructor() {}

  public static getInstance(): TRTCConfigService {
    if (!TRTCConfigService.instance) {
      TRTCConfigService.instance = new TRTCConfigService()
    }
    return TRTCConfigService.instance
  }

  // 获取 TRTC 配置
  public async getTRTCConfig(userId?: string): Promise<TRTCConfig> {
    try {
      // 如果已有配置且用户ID匹配，直接返回
      if (this.config && (!userId || this.config.userId === userId)) {
        return this.config
      }

      // 从后端获取配置
      const response = await axios.get('/api/user/im-user-sig', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      })

      if (response.data.code !== 0) {
        throw new Error(response.data.message || '获取TRTC配置失败')
      }

      const data = response.data.data
      
      // 构建配置对象
      this.config = {
        sdkAppId: parseInt(data.sdk_app_id),
        userSig: data.user_sig,
        userId: userId || this.getCurrentUserId()
      }

      return this.config
    } catch (error) {
      console.error('获取TRTC配置失败:', error)
      throw error
    }
  }

  // 获取当前用户ID
  private getCurrentUserId(): string {
    // 从 localStorage 获取用户信息
    const userInfo = localStorage.getItem('userInfo')
    if (userInfo) {
      try {
        const user = JSON.parse(userInfo)
        return user.id?.toString() || user.username || 'default'
      } catch (error) {
        console.warn('解析用户信息失败:', error)
      }
    }

    // 尝试从 token 中解析用户信息
    const token = localStorage.getItem('token')
    if (token) {
      try {
        // 简单解析 JWT token（仅用于获取用户ID）
        const payload = JSON.parse(atob(token.split('.')[1]))
        return payload.user_id?.toString() || payload.username || 'default'
      } catch (error) {
        console.warn('解析token失败:', error)
      }
    }

    // 如果没有用户信息，生成一个临时ID
    return `user_${Date.now()}`
  }

  // 清除配置缓存
  public clearConfig(): void {
    this.config = null
  }

  // 验证配置是否有效
  public isConfigValid(config: TRTCConfig): boolean {
    return !!(
      config &&
      config.sdkAppId &&
      config.sdkAppId !== 0 &&
      config.userId &&
      config.userSig
    )
  }
}

// 导出单例实例
export const trtcConfigService = TRTCConfigService.getInstance()
