{"version": 3, "file": "prefer-regexp-exec.js", "sourceRoot": "", "sources": ["../../src/rules/prefer-regexp-exec.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AAEA,oDAA0D;AAC1D,sDAAwC;AAGxC,kCAMiB;AAEjB,IAAK,YAKJ;AALD,WAAK,YAAY;IACf,iDAAS,CAAA;IACT,mDAAe,CAAA;IACf,mDAAe,CAAA;IACf,+CAAsB,CAAA;AACxB,CAAC,EALI,YAAY,KAAZ,YAAY,QAKhB;AAED,kBAAe,IAAA,iBAAU,EAAC;IACxB,IAAI,EAAE,oBAAoB;IAC1B,cAAc,EAAE,EAAE;IAElB,IAAI,EAAE;QACJ,IAAI,EAAE,YAAY;QAClB,OAAO,EAAE,MAAM;QACf,IAAI,EAAE;YACJ,WAAW,EACT,yEAAyE;YAC3E,oBAAoB,EAAE,IAAI;SAC3B;QACD,QAAQ,EAAE;YACR,yBAAyB,EAAE,yCAAyC;SACrE;QACD,MAAM,EAAE,EAAE;KACX;IAED,MAAM,CAAC,OAAO;QACZ,MAAM,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC,QAAQ,CAAC,OAAO,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC;QACxE,MAAM,QAAQ,GAAG,IAAA,wBAAiB,EAAC,OAAO,CAAC,CAAC;QAC5C,MAAM,OAAO,GAAG,QAAQ,CAAC,OAAO,CAAC,cAAc,EAAE,CAAC;QAElD;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAa;YACjC,OAAO,IAAA,kBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,KAAK,QAAQ,CAAC;QACjD,CAAC;QAED;;;WAGG;QACH,SAAS,YAAY,CAAC,IAAa;YACjC,OAAO,IAAA,kBAAW,EAAC,OAAO,EAAE,IAAI,CAAC,KAAK,QAAQ,CAAC;QACjD,CAAC;QAED,SAAS,oBAAoB,CAAC,KAAgB;YAC5C,IAAI,MAAM,GAAG,YAAY,CAAC,KAAK,CAAC;YAEhC,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE,CAAC;gBACzB,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;oBACvB,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC;gBAChC,CAAC;qBAAM,IAAI,YAAY,CAAC,IAAI,CAAC,EAAE,CAAC;oBAC9B,MAAM,IAAI,YAAY,CAAC,MAAM,CAAC;gBAChC,CAAC;YACH,CAAC;YAED,OAAO,MAAM,CAAC;QAChB,CAAC;QAED;;;;WAIG;QACH,SAAS,kCAAkC,CACzC,IAAqC;YAErC,IACE,CAAC,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,cAAc;gBAC1C,IAAI,CAAC,IAAI,KAAK,sBAAc,CAAC,aAAa,CAAC;gBAC7C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,sBAAc,CAAC,UAAU;gBAC9C,IAAI,CAAC,MAAM,CAAC,IAAI,KAAK,QAAQ,EAC7B,CAAC;gBACD,MAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;gBACnC,OAAO,CAAC,CACN,KAAK,EAAE,IAAI,KAAK,sBAAc,CAAC,OAAO;oBACtC,OAAO,KAAK,CAAC,KAAK,KAAK,QAAQ;oBAC/B,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAC1B,CAAC;YACJ,CAAC;YAED,OAAO,KAAK,CAAC;QACf,CAAC;QAED,OAAO;YACL,qGAAqG,CACnG,UAAqC;gBAErC,MAAM,UAAU,GAAG,UAAU,CAAC,MAAM,CAAC;gBACrC,MAAM,QAAQ,GAAG,UAAU,CAAC,MAAiC,CAAC;gBAC9D,MAAM,CAAC,YAAY,CAAC,GAAG,QAAQ,CAAC,SAAS,CAAC;gBAC1C,MAAM,aAAa,GAAG,IAAA,qBAAc,EAAC,YAAY,EAAE,WAAW,CAAC,CAAC;gBAEhE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,iBAAiB,CAAC,UAAU,CAAC,CAAC,EAAE,CAAC;oBAC1D,OAAO;gBACT,CAAC;gBAED,qDAAqD;gBACrD,IACE,CAAC,CAAC,aAAa;oBACb,CAAC,kCAAkC,CAAC,YAAY,CAAC,CAAC;oBACpD,CAAC,aAAa;wBACZ,aAAa,CAAC,KAAK,YAAY,MAAM;wBACrC,aAAa,CAAC,KAAK,CAAC,KAAK,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAC1C,CAAC;oBACD,OAAO;gBACT,CAAC;gBAED,IACE,YAAY,CAAC,IAAI,KAAK,sBAAc,CAAC,OAAO;oBAC5C,OAAO,YAAY,CAAC,KAAK,KAAK,QAAQ,EACtC,CAAC;oBACD,IAAI,MAAc,CAAC;oBACnB,IAAI,CAAC;wBACH,MAAM,GAAG,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;oBACtC,CAAC;oBAAC,MAAM,CAAC;wBACP,OAAO;oBACT,CAAC;oBACD,OAAO,OAAO,CAAC,MAAM,CAAC;wBACpB,IAAI,EAAE,UAAU,CAAC,QAAQ;wBACzB,SAAS,EAAE,2BAA2B;wBACtC,GAAG,EAAE,IAAA,uBAAgB,EAAC;4BACpB,UAAU,EAAE,OAAO,CAAC,UAAU;4BAC9B,IAAI,EAAE,QAAQ;4BACd,SAAS,EAAE,CAAC,UAAU,CAAC;4BACvB,IAAI,EAAE,UAAU,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,QAAQ,EAAE,SAAS,UAAU,GAAG;yBAC/D,CAAC;qBACH,CAAC,CAAC;gBACL,CAAC;gBAED,MAAM,YAAY,GAAG,QAAQ,CAAC,iBAAiB,CAAC,YAAY,CAAC,CAAC;gBAC9D,MAAM,aAAa,GAAG,oBAAoB,CACxC,OAAO,CAAC,cAAc,CAAC,YAAY,CAAC,CACrC,CAAC;gBACF,QAAQ,aAAa,EAAE,CAAC;oBACtB,KAAK,YAAY,CAAC,MAAM;wBACtB,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI,EAAE,UAAU,CAAC,QAAQ;4BACzB,SAAS,EAAE,2BAA2B;4BACtC,GAAG,EAAE,IAAA,uBAAgB,EAAC;gCACpB,UAAU,EAAE,OAAO,CAAC,UAAU;gCAC9B,IAAI,EAAE,QAAQ;gCACd,SAAS,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;gCACrC,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,CACjC,GAAG,YAAY,SAAS,UAAU,GAAG;6BACxC,CAAC;yBACH,CAAC,CAAC;oBAEL,KAAK,YAAY,CAAC,MAAM;wBACtB,OAAO,OAAO,CAAC,MAAM,CAAC;4BACpB,IAAI,EAAE,UAAU,CAAC,QAAQ;4BACzB,SAAS,EAAE,2BAA2B;4BACtC,GAAG,EAAE,IAAA,uBAAgB,EAAC;gCACpB,UAAU,EAAE,OAAO,CAAC,UAAU;gCAC9B,IAAI,EAAE,QAAQ;gCACd,SAAS,EAAE,CAAC,UAAU,EAAE,YAAY,CAAC;gCACrC,IAAI,EAAE,CAAC,UAAU,EAAE,YAAY,EAAE,EAAE,CACjC,UAAU,YAAY,UAAU,UAAU,GAAG;6BAChD,CAAC;yBACH,CAAC,CAAC;gBACP,CAAC;YACH,CAAC;SACF,CAAC;IACJ,CAAC;CACF,CAAC,CAAC"}