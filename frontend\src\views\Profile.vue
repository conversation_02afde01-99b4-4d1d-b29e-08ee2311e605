<template>
  <div class="profile-page">
    <div class="page-container">
      <!-- 用户信息卡片 -->
      <div class="user-card">
        <div class="user-avatar">
          <el-avatar :src="userStore.user?.avatar" :size="80">
            {{ userStore.user?.nickname?.charAt(0) }}
          </el-avatar>
          <el-button type="text" @click="handleAvatarUpload">
            <el-icon><Camera /></el-icon>
            更换头像
          </el-button>
        </div>
        <div class="user-info">
          <h2>{{ userStore.user?.nickname }}</h2>
          <p>{{ userStore.user?.email }}</p>
          <div class="user-stats">
            <div class="stat-item">
              <span class="stat-value">{{ userStore.user?.followers_count || 0 }}</span>
              <span class="stat-label">粉丝</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ userStore.user?.following_count || 0 }}</span>
              <span class="stat-label">关注</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ userStore.user?.room_count || 0 }}</span>
              <span class="stat-label">直播间</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 标签页 -->
      <el-tabs v-model="activeTab" class="profile-tabs">
        <el-tab-pane label="基本信息" name="basic">
          <div class="tab-content">
            <el-form
              ref="basicFormRef"
              :model="basicForm"
              :rules="basicRules"
              label-width="80px"
              class="basic-form"
            >
              <el-form-item label="昵称" prop="nickname">
                <el-input v-model="basicForm.nickname" />
              </el-form-item>
              <el-form-item label="邮箱" prop="email">
                <el-input v-model="basicForm.email" disabled />
              </el-form-item>
              <el-form-item label="手机号" prop="phone">
                <el-input v-model="basicForm.phone" />
              </el-form-item>
              <el-form-item label="个人简介" prop="bio">
                <el-input
                  v-model="basicForm.bio"
                  type="textarea"
                  :rows="4"
                  placeholder="介绍一下自己吧..."
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSaveBasic">
                  保存
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="安全设置" name="security">
          <div class="tab-content">
            <el-form
              ref="passwordFormRef"
              :model="passwordForm"
              :rules="passwordRules"
              label-width="80px"
              class="password-form"
            >
              <el-form-item label="当前密码" prop="currentPassword">
                <el-input
                  v-model="passwordForm.currentPassword"
                  type="password"
                  show-password
                />
              </el-form-item>
              <el-form-item label="新密码" prop="newPassword">
                <el-input
                  v-model="passwordForm.newPassword"
                  type="password"
                  show-password
                />
              </el-form-item>
              <el-form-item label="确认密码" prop="confirmPassword">
                <el-input
                  v-model="passwordForm.confirmPassword"
                  type="password"
                  show-password
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleChangePassword">
                  修改密码
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="主播设置" name="streamer">
          <div class="tab-content">
            <div v-if="!userStore.user?.is_streamer" class="apply-streamer">
              <el-alert
                title="申请成为主播"
                description="成为主播后，您可以开启直播间，与观众互动，获得收益。"
                type="info"
                :closable="false"
                show-icon
              />
              <el-form
                ref="streamerFormRef"
                :model="streamerForm"
                :rules="streamerRules"
                label-width="80px"
                class="streamer-form"
                style="margin-top: 20px"
              >
                <el-form-item label="真实姓名" prop="realName">
                  <el-input v-model="streamerForm.realName" />
                </el-form-item>
                <el-form-item label="身份证号" prop="idCard">
                  <el-input v-model="streamerForm.idCard" />
                </el-form-item>
                <el-form-item label="申请理由" prop="reason">
                  <el-input
                    v-model="streamerForm.reason"
                    type="textarea"
                    :rows="4"
                    placeholder="请说明您申请成为主播的理由..."
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleApplyStreamer">
                    提交申请
                  </el-button>
                </el-form-item>
              </el-form>
            </div>
            <div v-else class="streamer-info">
              <el-alert
                title="您已是认证主播"
                description="恭喜您已成为认证主播，可以开启直播间了！"
                type="success"
                :closable="false"
                show-icon
              />
              <div class="streamer-actions" style="margin-top: 20px">
                <el-button type="primary" @click="$router.push('/streamer')">
                  进入主播中心
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 头像上传对话框 -->
    <el-dialog v-model="avatarDialogVisible" title="更换头像" width="400px">
      <el-upload
        class="avatar-uploader"
        action="/api/upload"
        :show-file-list="false"
        :on-success="handleAvatarSuccess"
        :before-upload="beforeAvatarUpload"
      >
        <el-image
          v-if="newAvatar"
          :src="newAvatar"
          style="width: 200px; height: 200px"
          fit="cover"
        />
        <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
      </el-upload>
      <template #footer>
        <el-button @click="avatarDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSaveAvatar">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useUserStore } from '@/stores/user'
import { userApi } from '@/services/user'

const route = useRoute()
const userStore = useUserStore()

const activeTab = ref('basic')
const avatarDialogVisible = ref(false)
const newAvatar = ref('')

const basicFormRef = ref()
const basicForm = ref({
  nickname: '',
  email: '',
  phone: '',
  bio: ''
})

const passwordFormRef = ref()
const passwordForm = ref({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

const streamerFormRef = ref()
const streamerForm = ref({
  realName: '',
  idCard: '',
  reason: ''
})

const basicRules = {
  nickname: [
    { required: true, message: '请输入昵称', trigger: 'blur' }
  ]
}

const passwordRules = {
  currentPassword: [
    { required: true, message: '请输入当前密码', trigger: 'blur' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码', trigger: 'blur' },
    {
      validator: (rule: any, value: string, callback: Function) => {
        if (value !== passwordForm.value.newPassword) {
          callback(new Error('两次输入的密码不一致'))
        } else {
          callback()
        }
      },
      trigger: 'blur'
    }
  ]
}

const streamerRules = {
  realName: [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ],
  idCard: [
    { required: true, message: '请输入身份证号', trigger: 'blur' },
    { pattern: /^\d{17}[\dX]$/, message: '请输入正确的身份证号', trigger: 'blur' }
  ],
  reason: [
    { required: true, message: '请输入申请理由', trigger: 'blur' }
  ]
}

// 初始化表单数据
const initFormData = () => {
  if (userStore.user) {
    basicForm.value = {
      nickname: userStore.user.nickname,
      email: userStore.user.email,
      phone: userStore.user.phone || '',
      bio: userStore.user.bio || ''
    }
  }
}

// 保存基本信息
const handleSaveBasic = async () => {
  try {
    await basicFormRef.value.validate()
    await userApi.updateProfile(basicForm.value)
    await userStore.getUserInfo()
    ElMessage.success('保存成功')
  } catch (error: any) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败')
  }
}

// 修改密码
const handleChangePassword = async () => {
  try {
    await passwordFormRef.value.validate()
    await userApi.changePassword({
      current_password: passwordForm.value.currentPassword,
      new_password: passwordForm.value.newPassword
    })
    ElMessage.success('密码修改成功')
    passwordForm.value = {
      currentPassword: '',
      newPassword: '',
      confirmPassword: ''
    }
  } catch (error: any) {
    console.error('修改密码失败:', error)
    ElMessage.error(error.message || '修改密码失败')
  }
}

// 申请主播
const handleApplyStreamer = async () => {
  try {
    await streamerFormRef.value.validate()
    await userApi.applyStreamer(streamerForm.value)
    ElMessage.success('申请已提交，请等待审核')
  } catch (error: any) {
    console.error('申请失败:', error)
    ElMessage.error(error.message || '申请失败')
  }
}

// 头像上传
const handleAvatarUpload = () => {
  avatarDialogVisible.value = true
  newAvatar.value = ''
}

const handleAvatarSuccess = (response: any) => {
  newAvatar.value = response.url
}

const beforeAvatarUpload = (file: File) => {
  const isImage = file.type.startsWith('image/')
  const isLt2M = file.size / 1024 / 1024 < 2

  if (!isImage) {
    ElMessage.error('只能上传图片文件!')
    return false
  }
  if (!isLt2M) {
    ElMessage.error('图片大小不能超过 2MB!')
    return false
  }
  return true
}

const handleSaveAvatar = async () => {
  try {
    if (!newAvatar.value) {
      ElMessage.warning('请先上传头像')
      return
    }
    
    await userApi.updateAvatar({ avatar: newAvatar.value })
    await userStore.getUserInfo()
    avatarDialogVisible.value = false
    ElMessage.success('头像更新成功')
  } catch (error: any) {
    console.error('更新头像失败:', error)
    ElMessage.error(error.message || '更新头像失败')
  }
}

onMounted(() => {
  initFormData()
  
  // 检查URL参数，如果有tab参数则切换到对应标签页
  const tab = route.query.tab as string
  if (tab) {
    activeTab.value = tab
  }
})
</script>

<style scoped>
.profile-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.user-card {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  display: flex;
  gap: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.user-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.user-info {
  flex: 1;
}

.user-info h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.user-info p {
  margin: 0 0 16px 0;
  color: #909399;
}

.user-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
  color: #303133;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.profile-tabs {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 24px;
}

.basic-form,
.password-form,
.streamer-form {
  max-width: 500px;
}

.apply-streamer,
.streamer-info {
  max-width: 500px;
}

.avatar-uploader {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: border-color 0.3s;
  width: 200px;
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

.avatar-uploader:hover {
  border-color: #409eff;
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
}

@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .user-card {
    flex-direction: column;
    text-align: center;
  }
  
  .user-stats {
    justify-content: center;
  }
}
</style>
