<template>
  <div class="admin-categories">
    <div class="page-header">
      <h2>分类管理</h2>
      <div class="header-actions">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          添加分类
        </el-button>
      </div>
    </div>

    <div class="page-content">
      <el-table
        v-loading="loading"
        :data="categories"
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" />
        <el-table-column label="图标" width="80">
          <template #default="{ row }">
            <el-icon v-if="row.icon" size="24">
              <component :is="row.icon" />
            </el-icon>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="分类名称" />
        <el-table-column prop="description" label="描述" />
        <el-table-column prop="sort_order" label="排序" width="80" />
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-switch
              v-model="row.status"
              :active-value="1"
              :inactive-value="0"
              @change="handleToggleStatus(row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="room_count" label="直播间数量" width="120" />
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDate(row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150" fixed="right">
          <template #default="{ row }">
            <el-button
              type="primary"
              size="small"
              @click="handleEdit(row)"
            >
              编辑
            </el-button>
            <el-button
              type="danger"
              size="small"
              @click="handleDelete(row)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 添加/编辑分类对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="isEdit ? '编辑分类' : '添加分类'"
      width="500px"
    >
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入分类名称" />
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-select v-model="form.icon" placeholder="请选择图标">
            <el-option
              v-for="icon in iconOptions"
              :key="icon.value"
              :label="icon.label"
              :value="icon.value"
            >
              <div style="display: flex; align-items: center; gap: 8px;">
                <el-icon><component :is="icon.value" /></el-icon>
                <span>{{ icon.label }}</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort_order">
          <el-input-number
            v-model="form.sort_order"
            :min="0"
            :max="999"
            placeholder="排序值"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSave">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

const loading = ref(false)
const categories = ref([])
const dialogVisible = ref(false)
const isEdit = ref(false)
const formRef = ref()

const form = ref({
  id: 0,
  name: '',
  description: '',
  icon: '',
  sort_order: 0,
  status: 1
})

const rules = {
  name: [
    { required: true, message: '请输入分类名称', trigger: 'blur' }
  ],
  icon: [
    { required: true, message: '请选择图标', trigger: 'change' }
  ]
}

const iconOptions = [
  { label: '游戏', value: 'VideoPlay' },
  { label: '音乐', value: 'Headphone' },
  { label: '聊天', value: 'ChatDotRound' },
  { label: '教育', value: 'Reading' },
  { label: '体育', value: 'Football' },
  { label: '美食', value: 'Food' },
  { label: '旅游', value: 'MapLocation' },
  { label: '科技', value: 'Monitor' }
]

// 获取分类列表
const getCategoryList = async () => {
  try {
    loading.value = true
    // TODO: 调用API获取分类列表
    categories.value = [
      {
        id: 1,
        name: '游戏',
        description: '游戏直播分类',
        icon: 'VideoPlay',
        sort_order: 1,
        status: 1,
        room_count: 123,
        created_at: '2024-01-01 12:00:00'
      },
      {
        id: 2,
        name: '音乐',
        description: '音乐直播分类',
        icon: 'Headphone',
        sort_order: 2,
        status: 1,
        room_count: 45,
        created_at: '2024-01-01 12:00:00'
      }
    ]
  } catch (error) {
    console.error('获取分类列表失败:', error)
    ElMessage.error('获取分类列表失败')
  } finally {
    loading.value = false
  }
}

// 添加分类
const handleAdd = () => {
  isEdit.value = false
  form.value = {
    id: 0,
    name: '',
    description: '',
    icon: '',
    sort_order: 0,
    status: 1
  }
  dialogVisible.value = true
}

// 编辑分类
const handleEdit = (category: any) => {
  isEdit.value = true
  form.value = {
    id: category.id,
    name: category.name,
    description: category.description,
    icon: category.icon,
    sort_order: category.sort_order,
    status: category.status
  }
  dialogVisible.value = true
}

// 保存分类
const handleSave = async () => {
  try {
    await formRef.value.validate()
    
    if (isEdit.value) {
      // TODO: 调用API更新分类
      ElMessage.success('更新成功')
    } else {
      // TODO: 调用API添加分类
      ElMessage.success('添加成功')
    }
    
    dialogVisible.value = false
    getCategoryList()
  } catch (error) {
    console.error('保存失败:', error)
    ElMessage.error('保存失败')
  }
}

// 切换状态
const handleToggleStatus = async (category: any) => {
  try {
    // TODO: 调用API切换状态
    ElMessage.success('状态更新成功')
    getCategoryList()
  } catch (error) {
    console.error('状态更新失败:', error)
    ElMessage.error('状态更新失败')
    // 恢复原状态
    category.status = category.status === 1 ? 0 : 1
  }
}

// 删除分类
const handleDelete = async (category: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除分类 ${category.name} 吗？此操作不可恢复！`, '确认删除', {
      type: 'warning'
    })
    
    // TODO: 调用API删除分类
    ElMessage.success('删除成功')
    getCategoryList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

onMounted(() => {
  getCategoryList()
})
</script>

<style scoped>
.admin-categories {
  padding: 20px;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0;
  color: #303133;
}

.page-content {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
