package tencent

import (
	"crypto/md5"
	"fmt"
	"strconv"
	"time"

	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	live "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/live/v20180801"

	"live-platform/internal/config"
)

// LiveService 腾讯云直播服务
type LiveService struct {
	client *live.Client
	config *config.TencentLiveConfig
}

// NewLiveService 创建直播服务实例
func NewLiveService(cfg *config.TencentLiveConfig) (*LiveService, error) {
	credential := common.NewCredential(cfg.SecretID, cfg.SecretKey)

	cpf := profile.NewClientProfile()
	cpf.HttpProfile.Endpoint = "live.tencentcloudapi.com"
	cpf.HttpProfile.ReqMethod = "POST"
	cpf.HttpProfile.ReqTimeout = 30
	cpf.SignMethod = "HmacSHA256"

	client, err := live.NewClient(credential, cfg.Region, cpf)
	if err != nil {
		return nil, fmt.Errorf("failed to create live client: %w", err)
	}

	return &LiveService{
		client: client,
		config: cfg,
	}, nil
}

// PushURLInfo 推流地址信息
type PushURLInfo struct {
	URL        string `json:"url"`
	StreamName string `json:"stream_name"`
	ExpireTime int64  `json:"expire_time"`
}

// PlayURLInfo 播放地址信息
type PlayURLInfo struct {
	RTMPURL   string `json:"rtmp_url"`
	FLVURL    string `json:"flv_url"`
	HLSURL    string `json:"hls_url"`
	WebRTCURL string `json:"webrtc_url"`
}

// GeneratePushURL 生成推流地址
func (s *LiveService) GeneratePushURL(streamName string, expireTime int64) (*PushURLInfo, error) {
	if expireTime == 0 {
		expireTime = time.Now().Add(24 * time.Hour).Unix()
	}

	// 生成推流鉴权key
	authKey := s.generateAuthKey(streamName, expireTime)

	// 构建推流URL
	pushURL := fmt.Sprintf("rtmp://%s/live/%s?txSecret=%s&txTime=%s",
		s.config.PushDomain,
		streamName,
		authKey,
		strconv.FormatInt(expireTime, 16),
	)

	return &PushURLInfo{
		URL:        pushURL,
		StreamName: streamName,
		ExpireTime: expireTime,
	}, nil
}

// GenerateWebRTCPushURL 生成WebRTC推流地址
func (s *LiveService) GenerateWebRTCPushURL(streamName string, expireTime int64) (*PushURLInfo, error) {
	if expireTime == 0 {
		expireTime = time.Now().Add(24 * time.Hour).Unix()
	}

	// 生成推流鉴权key
	authKey := s.generateAuthKey(streamName, expireTime)

	// 构建WebRTC推流URL
	pushURL := fmt.Sprintf("webrtc://%s/live/%s?txSecret=%s&txTime=%s",
		s.config.PushDomain,
		streamName,
		authKey,
		strconv.FormatInt(expireTime, 16),
	)

	return &PushURLInfo{
		URL:        pushURL,
		StreamName: streamName,
		ExpireTime: expireTime,
	}, nil
}

// GeneratePlayURL 生成播放地址
func (s *LiveService) GeneratePlayURL(streamName string) *PlayURLInfo {
	return &PlayURLInfo{
		RTMPURL:   fmt.Sprintf("rtmp://%s/live/%s", s.config.PlayDomain, streamName),
		FLVURL:    fmt.Sprintf("http://%s/live/%s.flv", s.config.PlayDomain, streamName),
		HLSURL:    fmt.Sprintf("http://%s/live/%s.m3u8", s.config.PlayDomain, streamName),
		WebRTCURL: fmt.Sprintf("webrtc://%s/live/%s", s.config.PlayDomain, streamName),
	}
}

// generateAuthKey 生成推流鉴权key
func (s *LiveService) generateAuthKey(streamName string, expireTime int64) string {
	// txSecret = MD5(key + streamName + txTime)
	// key: 推流防盗链key
	// streamName: 流名称
	// txTime: 过期时间（16进制）

	txTime := strconv.FormatInt(expireTime, 16)
	hashValue := fmt.Sprintf("%s%s%s", s.config.PushKey, streamName, txTime)

	hash := md5.Sum([]byte(hashValue))
	return fmt.Sprintf("%x", hash)
}

// StreamStatus 流状态
type StreamStatus struct {
	StreamName string `json:"stream_name"`
	Status     int    `json:"status"` // 0-断流，1-推流中
	StartTime  string `json:"start_time"`
	EndTime    string `json:"end_time"`
}

// GetStreamStatus 获取流状态
func (s *LiveService) GetStreamStatus(streamName string) (*StreamStatus, error) {
	request := live.NewDescribeLiveStreamStateRequest()
	request.DomainName = &s.config.PushDomain
	request.StreamName = &streamName

	response, err := s.client.DescribeLiveStreamState(request)
	if err != nil {
		return nil, fmt.Errorf("failed to get stream status: %w", err)
	}

	status := &StreamStatus{
		StreamName: streamName,
	}

	if response.Response.StreamState != nil {
		switch *response.Response.StreamState {
		case "active":
			status.Status = 1
		default:
			status.Status = 0
		}
	}

	return status, nil
}

// DropLiveStream 断开直播流
func (s *LiveService) DropLiveStream(streamName string) error {
	request := live.NewDropLiveStreamRequest()
	request.DomainName = &s.config.PushDomain
	request.StreamName = &streamName

	_, err := s.client.DropLiveStream(request)
	if err != nil {
		return fmt.Errorf("failed to drop live stream: %w", err)
	}

	return nil
}

// ForbidLiveStream 禁播直播流
func (s *LiveService) ForbidLiveStream(streamName string, resumeTime *string) error {
	request := live.NewForbidLiveStreamRequest()
	request.DomainName = &s.config.PushDomain
	request.StreamName = &streamName

	if resumeTime != nil {
		request.ResumeTime = resumeTime
	}

	_, err := s.client.ForbidLiveStream(request)
	if err != nil {
		return fmt.Errorf("failed to forbid live stream: %w", err)
	}

	return nil
}

// ResumeLiveStream 恢复直播流
func (s *LiveService) ResumeLiveStream(streamName string) error {
	request := live.NewResumeLiveStreamRequest()
	request.DomainName = &s.config.PushDomain
	request.StreamName = &streamName

	_, err := s.client.ResumeLiveStream(request)
	if err != nil {
		return fmt.Errorf("failed to resume live stream: %w", err)
	}

	return nil
}

// RecordInfo 录制信息
type RecordInfo struct {
	RecordURL string `json:"record_url"`
	StartTime string `json:"start_time"`
	EndTime   string `json:"end_time"`
	Duration  int64  `json:"duration"`
	FileSize  int64  `json:"file_size"`
	VideoURL  string `json:"video_url"`
}

// GetRecordFiles 获取录制文件（暂时返回空列表，避免API兼容性问题）
func (s *LiveService) GetRecordFiles(streamName string, startTime, endTime string) ([]*RecordInfo, error) {
	// 暂时返回空列表，避免API版本兼容性问题
	// 后续可以根据实际需要实现具体的录制文件获取逻辑
	return []*RecordInfo{}, nil
}
