import { ElMessage } from 'element-plus'

// 腾讯云直播 WebRTC 推流配置接口
export interface WebRTCPushConfig {
  pushUrl: string
  userId?: string
  roomId?: number
}

// 推流状态枚举
export enum PushStatus {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  PUSHING = 'pushing',
  DISCONNECTED = 'disconnected',
  ERROR = 'error'
}

// 流质量配置接口
export interface StreamQualityConfig {
  videoQuality: string // '720p', '1080p' 等
  audioQuality: string // 'standard', 'high'
  frameRate?: number
  bitrate?: number
}

// 推流事件接口
export interface PushEvents {
  onStatusChange?: (status: PushStatus) => void
  onError?: (error: Error) => void
  onStreamPublished?: () => void
  onStreamUnpublished?: () => void
  onNetworkQuality?: (quality: any) => void
}

// 声明腾讯云直播推流器
declare global {
  interface Window {
    TXLivePusher: any
  }
}

export class WebRTCPushService {
  private pusher: any = null
  private config: WebRTCPushConfig | null = null
  private status: PushStatus = PushStatus.IDLE
  private events: Partial<PushEvents> = {}
  private isPushing = false
  private cameraStreamId: string | null = null
  private microphoneStreamId: string | null = null

  constructor() {
    this.initPusher()
  }

  private async initPusher() {
    try {
      // 检查浏览器支持性
      if (typeof window !== 'undefined' && window.TXLivePusher) {
        const support = await window.TXLivePusher.checkSupport()
        if (!support.isWebRTCSupported) {
          throw new Error('浏览器不支持 WebRTC')
        }
        
        this.pusher = new window.TXLivePusher()
        this.setupPusherEvents()
        this.setStatus(PushStatus.IDLE)
      } else {
        throw new Error('腾讯云直播推流 SDK 未加载')
      }
    } catch (error) {
      console.error('初始化推流器失败:', error)
      this.setStatus(PushStatus.ERROR)
    }
  }

  private setupPusherEvents() {
    if (!this.pusher) return

    // 设置推流事件回调
    this.pusher.setObserver({
      onError: (code: number, message: string, extraInfo: any) => {
        console.error('推流错误:', code, message, extraInfo)
        this.setStatus(PushStatus.ERROR)
        this.events.onError?.(new Error(`推流错误: ${message}`))
      },
      
      onWarning: (code: number, message: string, extraInfo: any) => {
        console.warn('推流警告:', code, message, extraInfo)
      },
      
      onPushStatusUpdate: (status: number, message: string, extraInfo: any) => {
        console.log('推流状态更新:', status, message)
        switch (status) {
          case 0: // DISCONNECTED
            this.setStatus(PushStatus.DISCONNECTED)
            this.isPushing = false
            break
          case 1: // CONNECTING
            this.setStatus(PushStatus.CONNECTING)
            break
          case 2: // CONNECTED
            this.setStatus(PushStatus.CONNECTED)
            break
          case 3: // RECONNECTING
            this.setStatus(PushStatus.CONNECTING)
            break
        }
      },
      
      onStatisticsUpdate: (statistics: any) => {
        // 推流统计数据
        this.events.onNetworkQuality?.(statistics)
      },
      
      onCaptureFirstVideoFrame: () => {
        console.log('首帧视频采集完成')
      },
      
      onCaptureFirstAudioFrame: () => {
        console.log('首帧音频采集完成')
      }
    })
  }

  // 设置事件监听器
  public on<K extends keyof PushEvents>(event: K, callback: PushEvents[K]) {
    this.events[event] = callback
  }

  // 移除事件监听器
  public off<K extends keyof PushEvents>(event: K) {
    delete this.events[event]
  }

  // 设置状态
  private setStatus(status: PushStatus) {
    if (this.status !== status) {
      this.status = status
      this.events.onStatusChange?.(status)
    }
  }

  // 获取当前状态
  public getStatus(): PushStatus {
    return this.status
  }

  // 初始化推流
  public async init(config: WebRTCPushConfig): Promise<void> {
    try {
      this.config = config
      
      if (!this.pusher) {
        await this.initPusher()
      }
      
      this.setStatus(PushStatus.IDLE)
    } catch (error) {
      console.error('初始化推流失败:', error)
      this.setStatus(PushStatus.ERROR)
      throw error
    }
  }

  // 设置预览容器
  public setRenderView(container: string | HTMLDivElement): void {
    if (this.pusher) {
      this.pusher.setRenderView(container)
    }
  }

  // 开始摄像头采集
  public async startCamera(deviceId?: string): Promise<string> {
    if (!this.pusher) {
      throw new Error('推流器未初始化')
    }

    try {
      this.cameraStreamId = await this.pusher.startCamera(deviceId)
      return this.cameraStreamId
    } catch (error) {
      console.error('开启摄像头失败:', error)
      throw error
    }
  }

  // 停止摄像头采集
  public stopCamera(): void {
    if (this.pusher && this.cameraStreamId) {
      this.pusher.stopCamera(this.cameraStreamId)
      this.cameraStreamId = null
    }
  }

  // 开始麦克风采集
  public async startMicrophone(deviceId?: string): Promise<string> {
    if (!this.pusher) {
      throw new Error('推流器未初始化')
    }

    try {
      this.microphoneStreamId = await this.pusher.startMicrophone(deviceId)
      return this.microphoneStreamId
    } catch (error) {
      console.error('开启麦克风失败:', error)
      throw error
    }
  }

  // 停止麦克风采集
  public stopMicrophone(): void {
    if (this.pusher && this.microphoneStreamId) {
      this.pusher.stopMicrophone(this.microphoneStreamId)
      this.microphoneStreamId = null
    }
  }

  // 设置视频质量
  public setVideoQuality(quality: string): void {
    if (this.pusher) {
      this.pusher.setVideoQuality(quality)
    }
  }

  // 设置音频质量
  public setAudioQuality(quality: string): void {
    if (this.pusher) {
      this.pusher.setAudioQuality(quality)
    }
  }

  // 开始推流
  public async startPush(qualityConfig?: StreamQualityConfig): Promise<void> {
    if (!this.pusher) {
      throw new Error('推流器未初始化')
    }

    if (!this.config?.pushUrl) {
      throw new Error('推流地址未配置')
    }

    try {
      // 设置质量参数
      if (qualityConfig) {
        this.setVideoQuality(qualityConfig.videoQuality)
        this.setAudioQuality(qualityConfig.audioQuality)
        
        if (qualityConfig.frameRate) {
          this.pusher.setProperty('setVideoFPS', qualityConfig.frameRate)
        }
        
        if (qualityConfig.bitrate) {
          this.pusher.setProperty('setVideoBitrate', qualityConfig.bitrate)
        }
      }

      // 开始推流
      await this.pusher.startPush(this.config.pushUrl)
      this.isPushing = true
      this.setStatus(PushStatus.PUSHING)
      this.events.onStreamPublished?.()
    } catch (error) {
      console.error('开始推流失败:', error)
      this.setStatus(PushStatus.ERROR)
      throw error
    }
  }

  // 停止推流
  public async stopPush(): Promise<void> {
    if (this.pusher && this.isPushing) {
      this.pusher.stopPush()
      this.isPushing = false
      this.setStatus(PushStatus.CONNECTED)
      this.events.onStreamUnpublished?.()
    }
  }

  // 检查是否正在推流
  public isCurrentlyPushing(): boolean {
    return this.isPushing && this.pusher?.isPushing()
  }

  // 销毁推流器
  public destroy(): void {
    if (this.pusher) {
      this.stopPush()
      this.stopCamera()
      this.stopMicrophone()
      this.pusher.destroy()
      this.pusher = null
    }
    this.setStatus(PushStatus.IDLE)
  }
}

// 导出单例实例
export const webrtcPushService = new WebRTCPushService()
