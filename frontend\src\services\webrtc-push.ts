import TRTC from 'trtc-js-sdk'
import { ElMessage } from 'element-plus'

// WebRTC推流配置接口
export interface WebRTCPushConfig {
  sdkAppId: number
  userId: string
  userSig: string
  roomId: number
  streamId?: string
  pushUrl?: string
}

// 推流质量配置
export interface StreamQualityConfig {
  video: {
    width: number
    height: number
    frameRate: number
    bitrate: number
  }
  audio: {
    bitrate: number
    sampleRate: number
  }
}

// 推流状态
export enum PushStatus {
  IDLE = 'idle',
  CONNECTING = 'connecting',
  CONNECTED = 'connected',
  PUSHING = 'pushing',
  ERROR = 'error',
  DISCONNECTED = 'disconnected'
}

// 事件类型
export interface PushEvents {
  onStatusChange: (status: PushStatus) => void
  onError: (error: Error) => void
  onStreamPublished: () => void
  onStreamUnpublished: () => void
  onNetworkQuality: (quality: any) => void
}

export class WebRTCPushService {
  private client: any = null
  private localStream: any = null
  private config: WebRTCPushConfig | null = null
  private status: PushStatus = PushStatus.IDLE
  private events: Partial<PushEvents> = {}
  private isInRoom = false

  constructor() {
    // 不在构造函数中初始化客户端，等待有效配置
  }

  private createClient(config: WebRTCPushConfig) {
    try {
      // 验证必要参数
      if (!config.sdkAppId || config.sdkAppId === 0) {
        throw new Error('sdkAppId 不能为空或0')
      }
      if (!config.userId) {
        throw new Error('userId 不能为空')
      }
      if (!config.userSig) {
        throw new Error('userSig 不能为空')
      }

      this.client = TRTC.createClient({
        mode: 'live', // 直播模式
        sdkAppId: config.sdkAppId,
        userId: config.userId,
        userSig: config.userSig
      })

      this.setupClientEvents()
    } catch (error) {
      console.error('创建TRTC客户端失败:', error)
      this.setStatus(PushStatus.ERROR)
      throw error
    }
  }

  private setupClientEvents() {
    if (!this.client) return

    // 连接状态事件
    this.client.on('client-banned', () => {
      console.warn('用户被踢出房间')
      this.setStatus(PushStatus.DISCONNECTED)
      this.events.onError?.(new Error('用户被踢出房间'))
    })

    this.client.on('connection-state-changed', (event: any) => {
      console.log('连接状态变化:', event.state)
      switch (event.state) {
        case 'CONNECTING':
          this.setStatus(PushStatus.CONNECTING)
          break
        case 'CONNECTED':
          this.setStatus(PushStatus.CONNECTED)
          break
        case 'DISCONNECTED':
          this.setStatus(PushStatus.DISCONNECTED)
          break
        case 'RECONNECTING':
          this.setStatus(PushStatus.CONNECTING)
          break
      }
    })

    // 网络质量事件
    this.client.on('network-quality', (event: any) => {
      this.events.onNetworkQuality?.(event)
    })

    // 错误事件
    this.client.on('error', (error: any) => {
      console.error('TRTC客户端错误:', error)
      this.setStatus(PushStatus.ERROR)
      this.events.onError?.(error)
    })
  }

  // 设置事件监听器
  public on<K extends keyof PushEvents>(event: K, callback: PushEvents[K]) {
    this.events[event] = callback
  }

  // 移除事件监听器
  public off<K extends keyof PushEvents>(event: K) {
    delete this.events[event]
  }

  // 设置推流状态
  private setStatus(status: PushStatus) {
    if (this.status !== status) {
      this.status = status
      this.events.onStatusChange?.(status)
    }
  }

  // 获取当前状态
  public getStatus(): PushStatus {
    return this.status
  }

  // 初始化推流
  public async init(config: WebRTCPushConfig): Promise<void> {
    try {
      this.config = config

      // 销毁现有客户端（如果存在）
      if (this.client) {
        this.client.destroy()
        this.client = null
      }

      // 创建新客户端
      this.createClient(config)
      this.setStatus(PushStatus.IDLE)
    } catch (error) {
      console.error('初始化推流失败:', error)
      this.setStatus(PushStatus.ERROR)
      throw error
    }
  }

  // 创建本地流
  public async createLocalStream(constraints: {
    video?: boolean | MediaTrackConstraints
    audio?: boolean | MediaTrackConstraints
    screen?: boolean
  }): Promise<any> {
    try {
      let streamConfig: any = {
        userId: this.config?.userId || 'default',
        audio: constraints.audio !== false,
        video: constraints.video !== false
      }

      if (constraints.screen) {
        // 屏幕分享
        streamConfig.screen = true
        delete streamConfig.video
      } else if (constraints.video && typeof constraints.video === 'object') {
        // 摄像头采集
        streamConfig.video = constraints.video
      }

      this.localStream = TRTC.createStream(streamConfig)
      await this.localStream.initialize()

      return this.localStream
    } catch (error) {
      console.error('创建本地流失败:', error)
      throw error
    }
  }

  // 进入房间
  public async enterRoom(): Promise<void> {
    if (!this.config || !this.client) {
      throw new Error('推流服务未初始化')
    }

    try {
      this.setStatus(PushStatus.CONNECTING)
      
      await this.client.join({
        roomId: this.config.roomId
      })

      this.isInRoom = true
      this.setStatus(PushStatus.CONNECTED)
    } catch (error) {
      console.error('进入房间失败:', error)
      this.setStatus(PushStatus.ERROR)
      throw error
    }
  }

  // 开始推流
  public async startPush(qualityConfig?: StreamQualityConfig): Promise<void> {
    if (!this.localStream || !this.client || !this.isInRoom) {
      throw new Error('推流条件不满足')
    }

    try {
      // 设置推流质量
      if (qualityConfig) {
        await this.setStreamQuality(qualityConfig)
      }

      // 发布本地流
      await this.client.publish(this.localStream)
      
      this.setStatus(PushStatus.PUSHING)
      this.events.onStreamPublished?.()
      
      ElMessage.success('开始推流成功')
    } catch (error) {
      console.error('开始推流失败:', error)
      this.setStatus(PushStatus.ERROR)
      throw error
    }
  }

  // 停止推流
  public async stopPush(): Promise<void> {
    try {
      if (this.localStream && this.client && this.isInRoom) {
        await this.client.unpublish(this.localStream)
        this.events.onStreamUnpublished?.()
      }

      if (this.localStream) {
        this.localStream.close()
        this.localStream = null
      }

      this.setStatus(PushStatus.CONNECTED)
      ElMessage.success('停止推流成功')
    } catch (error) {
      console.error('停止推流失败:', error)
      throw error
    }
  }

  // 离开房间
  public async leaveRoom(): Promise<void> {
    try {
      if (this.status === PushStatus.PUSHING) {
        await this.stopPush()
      }

      if (this.client && this.isInRoom) {
        await this.client.leave()
        this.isInRoom = false
      }

      this.setStatus(PushStatus.IDLE)
    } catch (error) {
      console.error('离开房间失败:', error)
      throw error
    }
  }

  // 设置推流质量
  private async setStreamQuality(config: StreamQualityConfig): Promise<void> {
    if (!this.localStream) return

    try {
      // 设置视频质量
      if (config.video) {
        await this.localStream.setVideoProfile({
          width: config.video.width,
          height: config.video.height,
          frameRate: config.video.frameRate,
          bitrate: config.video.bitrate
        })
      }

      // 设置音频质量
      if (config.audio) {
        await this.localStream.setAudioProfile({
          bitrate: config.audio.bitrate,
          sampleRate: config.audio.sampleRate
        })
      }
    } catch (error) {
      console.error('设置推流质量失败:', error)
      throw error
    }
  }

  // 获取本地流
  public getLocalStream(): any {
    return this.localStream
  }

  // 获取推流统计信息
  public async getStats(): Promise<any> {
    if (!this.client || !this.isInRoom) {
      return null
    }

    try {
      return await this.client.getTransportStats()
    } catch (error) {
      console.error('获取推流统计失败:', error)
      return null
    }
  }

  // 销毁推流服务
  public async destroy(): Promise<void> {
    try {
      await this.leaveRoom()
      
      if (this.client) {
        this.client.destroy()
        this.client = null
      }

      this.config = null
      this.events = {}
      this.setStatus(PushStatus.IDLE)
    } catch (error) {
      console.error('销毁推流服务失败:', error)
    }
  }
}

// 创建推流服务实例
export const webrtcPushService = new WebRTCPushService()
