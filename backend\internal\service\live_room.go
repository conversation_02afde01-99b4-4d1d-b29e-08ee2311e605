package service

import (
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"

	"live-platform/pkg/tencent"
)

// LiveRoomService 直播间服务
type LiveRoomService struct {
	db          *gorm.DB
	rdb         *redis.Client
	liveService *tencent.LiveService
	imService   *tencent.IMService
}

// NewLiveRoomService 创建直播间服务
func NewLiveRoomService(db *gorm.DB, rdb *redis.Client, liveService *tencent.LiveService, imService *tencent.IMService) *LiveRoomService {
	return &LiveRoomService{
		db:          db,
		rdb:         rdb,
		liveService: liveService,
		imService:   imService,
	}
}

// GeneratePushURL 生成RTMP推流地址
func (s *LiveRoomService) GeneratePushURL(streamName string, expireTime int64) (*tencent.PushURLInfo, error) {
	return s.liveService.GeneratePushURL(streamName, expireTime)
}

// GenerateWebRTCPushURL 生成WebRTC推流地址
func (s *LiveRoomService) GenerateWebRTCPushURL(streamName string, expireTime int64) (*tencent.PushURLInfo, error) {
	return s.liveService.GenerateWebRTCPushURL(streamName, expireTime)
}

// GeneratePlayURL 生成播放地址
func (s *LiveRoomService) GeneratePlayURL(streamName string) *tencent.PlayURLInfo {
	return s.liveService.GeneratePlayURL(streamName)
}

// GetStreamStatus 获取流状态
func (s *LiveRoomService) GetStreamStatus(streamName string) (*tencent.StreamStatus, error) {
	return s.liveService.GetStreamStatus(streamName)
}

// DropLiveStream 断开直播流
func (s *LiveRoomService) DropLiveStream(streamName string) error {
	return s.liveService.DropLiveStream(streamName)
}
