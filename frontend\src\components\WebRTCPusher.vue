<template>
  <div class="webrtc-pusher">
    <!-- 推流预览区域 -->
    <div class="preview-container">
      <div class="video-preview" ref="videoPreviewRef">
        <div v-if="!localStream" class="no-preview">
          <el-icon size="64"><VideoCamera /></el-icon>
          <p>请选择采集方式开始预览</p>
        </div>
      </div>
      
      <!-- 推流状态指示器 -->
      <div class="status-indicator" :class="statusClass">
        <el-icon><Connection /></el-icon>
        <span>{{ statusText }}</span>
      </div>
    </div>

    <!-- 控制面板 -->
    <div class="control-panel">
      <!-- 采集方式选择 -->
      <div class="capture-section">
        <h3>采集方式</h3>
        <el-radio-group v-model="captureMode" @change="handleCaptureModeChange">
          <el-radio value="camera">摄像头采集</el-radio>
          <el-radio value="screen">屏幕分享</el-radio>
          <el-radio value="file">本地文件</el-radio>
        </el-radio-group>
      </div>

      <!-- 摄像头设置 -->
      <div v-if="captureMode === 'camera'" class="camera-settings">
        <el-row :gutter="16">
          <el-col :span="12">
            <el-select v-model="selectedCamera" placeholder="选择摄像头" @change="updateCameraDevice">
              <el-option
                v-for="device in cameraDevices"
                :key="device.deviceId"
                :label="device.label"
                :value="device.deviceId"
              />
            </el-select>
          </el-col>
          <el-col :span="12">
            <el-select v-model="selectedMicrophone" placeholder="选择麦克风" @change="updateMicrophoneDevice">
              <el-option
                v-for="device in microphoneDevices"
                :key="device.deviceId"
                :label="device.label"
                :value="device.deviceId"
              />
            </el-select>
          </el-col>
        </el-row>
      </div>

      <!-- 本地文件选择 -->
      <div v-if="captureMode === 'file'" class="file-settings">
        <el-upload
          ref="uploadRef"
          :auto-upload="false"
          :show-file-list="false"
          accept="video/*,audio/*"
          @change="handleFileSelect"
        >
          <el-button type="primary">选择本地文件</el-button>
        </el-upload>
        <p v-if="selectedFile" class="selected-file">
          已选择: {{ selectedFile.name }}
        </p>
      </div>

      <!-- 质量设置 -->
      <div class="quality-section">
        <h3>推流质量</h3>
        <el-row :gutter="16">
          <el-col :span="8">
            <el-select v-model="qualityPreset" @change="applyQualityPreset">
              <el-option label="流畅 (480p)" value="smooth" />
              <el-option label="标清 (720p)" value="standard" />
              <el-option label="高清 (1080p)" value="high" />
              <el-option label="自定义" value="custom" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-input-number
              v-model="qualityConfig.video.bitrate"
              :min="100"
              :max="8000"
              controls-position="right"
              placeholder="视频码率(kbps)"
            />
          </el-col>
          <el-col :span="8">
            <el-input-number
              v-model="qualityConfig.video.frameRate"
              :min="15"
              :max="60"
              controls-position="right"
              placeholder="帧率(fps)"
            />
          </el-col>
        </el-row>
      </div>

      <!-- 推流配置 -->
      <div class="stream-config-section">
        <h3>推流配置</h3>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="房间ID:">
              <el-input-number v-model="roomId" :min="1" :max="999999" :disabled="isPushing" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户ID:">
              <el-input v-model="userId" placeholder="用户ID" :disabled="isPushing" />
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 控制按钮 -->
      <div class="action-buttons">
        <el-button
          v-if="!isPreviewActive"
          type="primary"
          @click="startPreview"
          :disabled="!canStartPreview"
        >
          开始预览
        </el-button>
        <el-button
          v-else
          @click="stopPreview"
        >
          停止预览
        </el-button>

        <el-button
          v-if="!isPushing"
          type="success"
          @click="startPush"
          :disabled="!canStartPush"
          :loading="pushLoading"
        >
          开始推流
        </el-button>
        <el-button
          v-else
          type="danger"
          @click="stopPush"
          :loading="pushLoading"
        >
          停止推流
        </el-button>
      </div>
    </div>

    <!-- 推流统计信息 -->
    <div v-if="isPushing" class="stats-panel">
      <h3>推流统计</h3>
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="stat-item">
            <span class="label">视频码率:</span>
            <span class="value">{{ stats.videoBitrate || 0 }} kbps</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <span class="label">音频码率:</span>
            <span class="value">{{ stats.audioBitrate || 0 }} kbps</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <span class="label">帧率:</span>
            <span class="value">{{ stats.frameRate || 0 }} fps</span>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="stat-item">
            <span class="label">网络质量:</span>
            <span class="value" :class="networkQualityClass">{{ networkQualityText }}</span>
          </div>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { VideoCamera, Connection } from '@element-plus/icons-vue'
import { webrtcPushService, PushStatus, type StreamQualityConfig, type WebRTCPushConfig } from '@/services/webrtc-push'
import { trtcConfigService } from '@/services/trtc-config'

// Props
interface Props {
  roomId?: number
  userId?: string
  autoStart?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  roomId: 0,
  userId: '',
  autoStart: false
})

// Emits
const emit = defineEmits<{
  pushStarted: []
  pushStopped: []
  error: [error: Error]
}>()

// 响应式数据
const videoPreviewRef = ref<HTMLElement>()
const captureMode = ref<'camera' | 'screen' | 'file'>('camera')
const selectedCamera = ref('')
const selectedMicrophone = ref('')
const selectedFile = ref<File | null>(null)
const qualityPreset = ref('standard')
// 推流配置
const roomId = ref(props.roomId || 0)
const userId = ref(props.userId || '')
const pushLoading = ref(false)

// 设备列表
const cameraDevices = ref<MediaDeviceInfo[]>([])
const microphoneDevices = ref<MediaDeviceInfo[]>([])

// 推流状态
const pushStatus = ref<PushStatus>(PushStatus.IDLE)
const localStream = ref<any>(null)
const isPreviewActive = ref(false)
const networkQuality = ref(0)

// 推流统计
const stats = ref({
  videoBitrate: 0,
  audioBitrate: 0,
  frameRate: 0,
  packetLoss: 0
})

// 质量配置
const qualityConfig = ref<StreamQualityConfig>({
  video: {
    width: 1280,
    height: 720,
    frameRate: 30,
    bitrate: 2000
  },
  audio: {
    bitrate: 128,
    sampleRate: 48000
  }
})

// 计算属性
const isPushing = computed(() => pushStatus.value === PushStatus.PUSHING)
const canStartPreview = computed(() => {
  if (captureMode.value === 'camera') {
    return selectedCamera.value && selectedMicrophone.value
  } else if (captureMode.value === 'file') {
    return selectedFile.value
  }
  return true
})

const canStartPush = computed(() => {
  return isPreviewActive.value && roomId.value > 0 && userId.value.trim() !== ''
})

const statusClass = computed(() => {
  switch (pushStatus.value) {
    case PushStatus.PUSHING:
      return 'status-pushing'
    case PushStatus.CONNECTED:
      return 'status-connected'
    case PushStatus.CONNECTING:
      return 'status-connecting'
    case PushStatus.ERROR:
      return 'status-error'
    default:
      return 'status-idle'
  }
})

const statusText = computed(() => {
  switch (pushStatus.value) {
    case PushStatus.PUSHING:
      return '推流中'
    case PushStatus.CONNECTED:
      return '已连接'
    case PushStatus.CONNECTING:
      return '连接中'
    case PushStatus.ERROR:
      return '连接错误'
    default:
      return '未连接'
  }
})

const networkQualityClass = computed(() => {
  if (networkQuality.value >= 4) return 'quality-excellent'
  if (networkQuality.value >= 3) return 'quality-good'
  if (networkQuality.value >= 2) return 'quality-fair'
  return 'quality-poor'
})

const networkQualityText = computed(() => {
  if (networkQuality.value >= 4) return '优秀'
  if (networkQuality.value >= 3) return '良好'
  if (networkQuality.value >= 2) return '一般'
  return '较差'
})

// 生命周期
onMounted(async () => {
  await initDevices()
  setupPushService()

  // 同步 props 到本地状态
  roomId.value = props.roomId || 0
  userId.value = props.userId || ''
})

onUnmounted(() => {
  cleanup()
})

// 初始化设备列表
const initDevices = async () => {
  try {
    const devices = await navigator.mediaDevices.enumerateDevices()

    cameraDevices.value = devices.filter(device => device.kind === 'videoinput')
    microphoneDevices.value = devices.filter(device => device.kind === 'audioinput')

    // 设置默认设备
    if (cameraDevices.value.length > 0) {
      selectedCamera.value = cameraDevices.value[0].deviceId
    }
    if (microphoneDevices.value.length > 0) {
      selectedMicrophone.value = microphoneDevices.value[0].deviceId
    }
  } catch (error) {
    console.error('获取设备列表失败:', error)
    ElMessage.error('获取设备列表失败')
  }
}

// 设置推流服务
const setupPushService = () => {
  webrtcPushService.on('onStatusChange', (status) => {
    pushStatus.value = status
  })

  webrtcPushService.on('onError', (error) => {
    ElMessage.error(`推流错误: ${error.message}`)
    emit('error', error)
  })

  webrtcPushService.on('onStreamPublished', () => {
    emit('pushStarted')
    startStatsMonitoring()
  })

  webrtcPushService.on('onStreamUnpublished', () => {
    emit('pushStopped')
    stopStatsMonitoring()
  })

  webrtcPushService.on('onNetworkQuality', (quality) => {
    networkQuality.value = quality.uplinkQuality || 0
  })
}

// 处理采集方式变化
const handleCaptureModeChange = () => {
  if (isPreviewActive.value) {
    stopPreview()
  }
}

// 更新摄像头设备
const updateCameraDevice = () => {
  if (isPreviewActive.value) {
    restartPreview()
  }
}

// 更新麦克风设备
const updateMicrophoneDevice = () => {
  if (isPreviewActive.value) {
    restartPreview()
  }
}

// 处理文件选择
const handleFileSelect = (file: any) => {
  selectedFile.value = file.raw
}

// 应用质量预设
const applyQualityPreset = () => {
  switch (qualityPreset.value) {
    case 'smooth':
      qualityConfig.value = {
        video: { width: 854, height: 480, frameRate: 25, bitrate: 1000 },
        audio: { bitrate: 64, sampleRate: 44100 }
      }
      break
    case 'standard':
      qualityConfig.value = {
        video: { width: 1280, height: 720, frameRate: 30, bitrate: 2000 },
        audio: { bitrate: 128, sampleRate: 48000 }
      }
      break
    case 'high':
      qualityConfig.value = {
        video: { width: 1920, height: 1080, frameRate: 30, bitrate: 4000 },
        audio: { bitrate: 192, sampleRate: 48000 }
      }
      break
  }
}



// 开始预览
const startPreview = async () => {
  try {
    let constraints: any = {}

    if (captureMode.value === 'camera') {
      constraints = {
        video: {
          deviceId: selectedCamera.value,
          width: qualityConfig.value.video.width,
          height: qualityConfig.value.video.height,
          frameRate: qualityConfig.value.video.frameRate
        },
        audio: {
          deviceId: selectedMicrophone.value
        }
      }
    } else if (captureMode.value === 'screen') {
      constraints = {
        screen: true,
        audio: true
      }
    } else if (captureMode.value === 'file') {
      // 文件采集需要特殊处理
      constraints = {
        video: false,
        audio: false
      }
    }

    localStream.value = await webrtcPushService.createLocalStream(constraints)

    // 在预览容器中播放
    await nextTick()
    if (videoPreviewRef.value && localStream.value) {
      localStream.value.play(videoPreviewRef.value)
    }

    isPreviewActive.value = true
    ElMessage.success('预览开始成功')
  } catch (error) {
    console.error('开始预览失败:', error)
    ElMessage.error('开始预览失败')
  }
}

// 停止预览
const stopPreview = () => {
  if (localStream.value) {
    localStream.value.stop()
    localStream.value = null
  }
  isPreviewActive.value = false
}

// 重启预览
const restartPreview = async () => {
  stopPreview()
  await startPreview()
}

// 开始推流
const startPush = async () => {
  if (!pushUrl.value) {
    ElMessage.error('请先生成推流地址')
    return
  }

  pushLoading.value = true
  try {
    // 获取 TRTC 配置
    const trtcConfig = await trtcConfigService.getTRTCConfig(props.userId)

    // 验证配置
    if (!trtcConfigService.isConfigValid(trtcConfig)) {
      throw new Error('TRTC配置无效，请检查用户登录状态')
    }

    // 初始化推流配置
    const config: WebRTCPushConfig = {
      sdkAppId: trtcConfig.sdkAppId,
      userId: trtcConfig.userId,
      userSig: trtcConfig.userSig,
      roomId: props.roomId,
      pushUrl: pushUrl.value
    }

    console.log('使用TRTC配置:', {
      sdkAppId: config.sdkAppId,
      userId: config.userId,
      roomId: config.roomId
    })

    await webrtcPushService.init(config)
    await webrtcPushService.enterRoom()
    await webrtcPushService.startPush(qualityConfig.value)

    ElMessage.success('推流开始成功')
  } catch (error) {
    console.error('开始推流失败:', error)
    ElMessage.error(`开始推流失败: ${error.message || error}`)
  } finally {
    pushLoading.value = false
  }
}

// 停止推流
const stopPush = async () => {
  pushLoading.value = true
  try {
    await webrtcPushService.stopPush()
    ElMessage.success('推流停止成功')
  } catch (error) {
    console.error('停止推流失败:', error)
    ElMessage.error('停止推流失败')
  } finally {
    pushLoading.value = false
  }
}

// 开始统计监控
let statsInterval: number | null = null
const startStatsMonitoring = () => {
  statsInterval = window.setInterval(async () => {
    const currentStats = await webrtcPushService.getStats()
    if (currentStats) {
      stats.value = {
        videoBitrate: Math.round(currentStats.video?.bytesSent / 1024 || 0),
        audioBitrate: Math.round(currentStats.audio?.bytesSent / 1024 || 0),
        frameRate: currentStats.video?.frameRate || 0,
        packetLoss: currentStats.video?.packetsLost || 0
      }
    }
  }, 1000)
}

// 停止统计监控
const stopStatsMonitoring = () => {
  if (statsInterval) {
    clearInterval(statsInterval)
    statsInterval = null
  }
}

// 清理资源
const cleanup = async () => {
  stopStatsMonitoring()
  stopPreview()
  await webrtcPushService.destroy()
}
</script>

<style scoped>
.webrtc-pusher {
  display: flex;
  flex-direction: column;
  gap: 20px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
}

.preview-container {
  position: relative;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.video-preview {
  width: 100%;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000;
}

.no-preview {
  text-align: center;
  color: #666;
}

.no-preview .el-icon {
  margin-bottom: 10px;
}

.status-indicator {
  position: absolute;
  top: 10px;
  right: 10px;
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status-idle {
  background: rgba(144, 147, 153, 0.8);
  color: white;
}

.status-connecting {
  background: rgba(230, 162, 60, 0.8);
  color: white;
}

.status-connected {
  background: rgba(103, 194, 58, 0.8);
  color: white;
}

.status-pushing {
  background: rgba(64, 158, 255, 0.8);
  color: white;
}

.status-error {
  background: rgba(245, 108, 108, 0.8);
  color: white;
}

.control-panel {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.control-panel h3 {
  margin: 0 0 15px 0;
  font-size: 16px;
  color: #303133;
}

.capture-section,
.quality-section,
.stream-url-section {
  margin-bottom: 20px;
}

.camera-settings,
.file-settings {
  margin-top: 15px;
}

.selected-file {
  margin-top: 10px;
  color: #67c23a;
  font-size: 14px;
}

.action-buttons {
  display: flex;
  gap: 10px;
  justify-content: center;
  margin-top: 20px;
}

.stats-panel {
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #ebeef5;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-item .label {
  font-weight: bold;
  color: #606266;
}

.stat-item .value {
  color: #303133;
}

.quality-excellent {
  color: #67c23a;
}

.quality-good {
  color: #e6a23c;
}

.quality-fair {
  color: #f56c6c;
}

.quality-poor {
  color: #f56c6c;
}
</style>
