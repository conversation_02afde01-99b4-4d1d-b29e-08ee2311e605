require('../../modules/es6.number.constructor');
require('../../modules/es6.number.epsilon');
require('../../modules/es6.number.is-finite');
require('../../modules/es6.number.is-integer');
require('../../modules/es6.number.is-nan');
require('../../modules/es6.number.is-safe-integer');
require('../../modules/es6.number.max-safe-integer');
require('../../modules/es6.number.min-safe-integer');
require('../../modules/es6.number.parse-float');
require('../../modules/es6.number.parse-int');
require('../../modules/es6.number.to-fixed');
require('../../modules/es6.number.to-precision');
require('../../modules/core.number.iterator');
module.exports = require('../../modules/_core').Number;
