package api

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"live-platform/internal/api/handler"
	"live-platform/internal/config"
	"live-platform/internal/middleware"
	"live-platform/internal/service"
	"live-platform/pkg/database"
	"live-platform/pkg/redis"
	"live-platform/pkg/tencent"
)

// NewRouter 创建路由
func NewRouter(cfg *config.Config) *gin.Engine {
	router := gin.New()

	// 中间件
	router.Use(gin.Logger())
	router.Use(gin.Recovery())
	router.Use(middleware.CORS(cfg))

	// 健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{
			"status":  "ok",
			"message": "Live platform is running",
		})
	})

	// 初始化服务
	db := database.GetDB()
	rdb := redis.GetRedis()

	// 腾讯云服务
	liveService, err := tencent.NewLiveService(&cfg.Tencent.Live)
	if err != nil {
		panic("Failed to init live service: " + err.Error())
	}

	imService, err := tencent.NewIMService(&cfg.Tencent.IM)
	if err != nil {
		panic("Failed to init IM service: " + err.Error())
	}

	// 业务服务
	userService := service.NewUserService(db, rdb)
	liveRoomService := service.NewLiveRoomService(db, rdb, liveService, imService)
	giftService := service.NewGiftService(db, rdb)

	// 处理器
	userHandler := handler.NewUserHandler(userService, imService, cfg)
	liveRoomHandler := handler.NewLiveRoomHandler(liveRoomService, cfg)
	giftHandler := handler.NewGiftHandler(giftService, cfg)

	// API路由组
	api := router.Group("/api")
	{
		// 认证相关
		auth := api.Group("/auth")
		{
			auth.POST("/register", userHandler.Register)
			auth.POST("/login", userHandler.Login)
			auth.POST("/logout", middleware.Auth(), userHandler.Logout)
			auth.POST("/refresh", middleware.Auth(), userHandler.RefreshToken)
		}

		// 用户相关
		user := api.Group("/user")
		user.Use(middleware.Auth())
		{
			user.GET("/info", userHandler.GetUserInfo)
			user.PUT("/info", userHandler.UpdateUserInfo)
			user.PUT("/password", userHandler.ChangePassword)
			user.POST("/avatar", userHandler.UploadAvatar)
			user.POST("/apply-streamer", userHandler.ApplyStreamer)
			user.GET("/stats", userHandler.GetUserStats)
		}

		// 直播间相关
		live := api.Group("/live")
		{
			live.GET("/rooms", liveRoomHandler.GetLiveRoomList)
			live.GET("/rooms/:roomId", liveRoomHandler.GetLiveRoom)
			live.GET("/categories", liveRoomHandler.GetCategories)
			live.GET("/hot", liveRoomHandler.GetHotRooms)
			live.GET("/stats", liveRoomHandler.GetLiveStats)

			// 测试用的推流地址生成API（无需权限验证）
			live.POST("/test/generate-push-url", liveRoomHandler.GenerateTestPushURL)

			// 需要登录的接口
			authenticated := live.Group("")
			authenticated.Use(middleware.Auth())
			{
				authenticated.POST("/rooms", middleware.RequireStreamer(), liveRoomHandler.CreateLiveRoom)
				authenticated.PUT("/rooms/:roomId", middleware.RequireOwner(), liveRoomHandler.UpdateLiveRoom)
				authenticated.DELETE("/rooms/:roomId", middleware.RequireOwner(), liveRoomHandler.DeleteLiveRoom)
				authenticated.POST("/rooms/:roomId/start", middleware.RequireOwner(), liveRoomHandler.StartLive)
				authenticated.POST("/rooms/:roomId/stop", middleware.RequireOwner(), liveRoomHandler.StopLive)
				authenticated.POST("/rooms/:roomId/join", liveRoomHandler.JoinRoom)
				authenticated.POST("/rooms/:roomId/leave", liveRoomHandler.LeaveRoom)
				authenticated.POST("/rooms/:roomId/like", liveRoomHandler.LikeRoom)
			}
		}

		// 礼物相关
		gift := api.Group("/gift")
		{
			gift.GET("/list", giftHandler.GetGiftList)

			// 需要登录的接口
			authenticated := gift.Group("")
			authenticated.Use(middleware.Auth())
			{
				authenticated.POST("/send", giftHandler.SendGift)
				authenticated.GET("/records", giftHandler.GetGiftRecords)
			}
		}

		// IM相关
		im := api.Group("/im")
		im.Use(middleware.Auth())
		{
			im.GET("/user-sig", userHandler.GetIMUserSig)
		}

		// 管理员相关
		admin := api.Group("/admin")
		admin.Use(middleware.Auth(), middleware.RequireAdmin())
		{
			// 用户管理
			adminUser := admin.Group("/users")
			{
				adminUser.GET("", userHandler.GetUserList)
				adminUser.PUT("/:userId/status", userHandler.UpdateUserStatus)
				adminUser.PUT("/:userId/streamer-status", userHandler.ReviewStreamerApplication)
				adminUser.PUT("/:userId/reset-password", userHandler.ResetUserPassword)
				adminUser.DELETE("/:userId", userHandler.DeleteUser)
			}

			// 直播间管理
			adminLive := admin.Group("/rooms")
			{
				adminLive.GET("", liveRoomHandler.GetLiveRoomListAdmin)
				adminLive.PUT("/:roomId/status", liveRoomHandler.UpdateLiveRoomStatus)
				adminLive.DELETE("/:roomId", liveRoomHandler.DeleteLiveRoomAdmin)
			}

			// 分类管理
			adminCategory := admin.Group("/categories")
			{
				adminCategory.GET("", liveRoomHandler.GetCategoriesAdmin)
				adminCategory.POST("", liveRoomHandler.CreateCategory)
				adminCategory.PUT("/:categoryId", liveRoomHandler.UpdateCategory)
				adminCategory.DELETE("/:categoryId", liveRoomHandler.DeleteCategory)
			}

			// 礼物管理
			adminGift := admin.Group("/gifts")
			{
				adminGift.GET("", giftHandler.GetGiftListAdmin)
				adminGift.POST("", giftHandler.CreateGift)
				adminGift.PUT("/:giftId", giftHandler.UpdateGift)
				adminGift.DELETE("/:giftId", giftHandler.DeleteGift)
			}

			// 主播申请管理
			admin.GET("/streamer-applications", userHandler.GetStreamerApplications)

			// 统计数据
			admin.GET("/dashboard", handler.GetDashboardStats)
		}
	}

	// 静态文件服务
	router.Static("/uploads", "./uploads")

	return router
}
