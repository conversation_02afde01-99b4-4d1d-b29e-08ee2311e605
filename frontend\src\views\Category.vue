<template>
  <div class="category-page">
    <div class="page-container">
      <!-- 分类头部 -->
      <div class="category-header">
        <div class="category-info">
          <el-icon v-if="category.icon" size="32">
            <component :is="category.icon" />
          </el-icon>
          <div class="category-details">
            <h1>{{ category.name }}</h1>
            <p>{{ category.description }}</p>
          </div>
        </div>
        <div class="category-stats">
          <span>{{ total }} 个直播间</span>
        </div>
      </div>

      <!-- 筛选和排序 -->
      <div class="filter-bar">
        <div class="filter-left">
          <el-radio-group v-model="statusFilter" @change="handleFilterChange">
            <el-radio-button label="">全部</el-radio-button>
            <el-radio-button label="1">直播中</el-radio-button>
            <el-radio-button label="0">未开播</el-radio-button>
          </el-radio-group>
        </div>
        <div class="filter-right">
          <el-select v-model="sortBy" @change="handleSortChange" style="width: 120px">
            <el-option label="人气" value="viewer_count" />
            <el-option label="最新" value="created_at" />
            <el-option label="点赞" value="like_count" />
          </el-select>
        </div>
      </div>

      <!-- 直播间列表 -->
      <div class="rooms-grid" v-loading="loading">
        <RoomCard
          v-for="room in rooms"
          :key="room.id"
          :room="room"
          @click="$router.push(`/live/${room.id}`)"
        />
      </div>

      <!-- 空状态 -->
      <div v-if="!loading && rooms.length === 0" class="empty-state">
        <el-empty description="暂无直播间" />
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more">
        <el-button
          :loading="loading"
          @click="loadMore"
          style="width: 200px"
        >
          加载更多
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import RoomCard from '@/components/RoomCard.vue'
import { liveApi } from '@/services/live'
import type { LiveRoom, LiveCategory } from '@/types/live'

const route = useRoute()

const loading = ref(false)
const category = ref<LiveCategory>({
  id: 0,
  name: '',
  description: '',
  icon: ''
})
const rooms = ref<LiveRoom[]>([])
const total = ref(0)
const hasMore = ref(true)
const page = ref(1)
const statusFilter = ref('')
const sortBy = ref('viewer_count')

// 获取分类信息
const getCategoryInfo = async () => {
  try {
    const categoryId = route.params.categoryId as string
    const data = await liveApi.getCategoryInfo(parseInt(categoryId))
    category.value = data
  } catch (error) {
    console.error('获取分类信息失败:', error)
    ElMessage.error('获取分类信息失败')
  }
}

// 获取直播间列表
const getRoomList = async (reset = false) => {
  try {
    loading.value = true
    
    if (reset) {
      page.value = 1
      rooms.value = []
    }
    
    const categoryId = route.params.categoryId as string
    const data = await liveApi.getLiveRoomList({
      category_id: parseInt(categoryId),
      status: statusFilter.value ? parseInt(statusFilter.value) : undefined,
      sort_by: sortBy.value,
      page: page.value,
      page_size: 20
    })
    
    if (reset) {
      rooms.value = data.list
    } else {
      rooms.value.push(...data.list)
    }
    
    total.value = data.total
    hasMore.value = rooms.value.length < data.total
  } catch (error) {
    console.error('获取直播间列表失败:', error)
    ElMessage.error('获取直播间列表失败')
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = async () => {
  if (loading.value) return
  
  page.value++
  await getRoomList()
}

// 筛选变化
const handleFilterChange = () => {
  getRoomList(true)
}

// 排序变化
const handleSortChange = () => {
  getRoomList(true)
}

// 监听路由变化
watch(() => route.params.categoryId, () => {
  if (route.params.categoryId) {
    getCategoryInfo()
    getRoomList(true)
  }
})

onMounted(() => {
  getCategoryInfo()
  getRoomList(true)
})
</script>

<style scoped>
.category-page {
  min-height: 100vh;
  background: #f5f7fa;
}

.page-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.category-header {
  background: #fff;
  border-radius: 8px;
  padding: 24px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.category-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.category-details h1 {
  margin: 0 0 8px 0;
  font-size: 24px;
  color: #303133;
}

.category-details p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.category-stats {
  color: #909399;
  font-size: 14px;
}

.filter-bar {
  background: #fff;
  border-radius: 8px;
  padding: 16px 24px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.rooms-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 40px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.load-more {
  text-align: center;
  padding: 20px 0;
}

@media (max-width: 768px) {
  .page-container {
    padding: 10px;
  }
  
  .category-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .filter-bar {
    flex-direction: column;
    gap: 12px;
  }
  
  .rooms-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
  }
}
</style>
