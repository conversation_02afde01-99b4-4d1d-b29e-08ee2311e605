<template>
  <div class="live-room">
    <div class="room-container">
      <!-- 视频播放区域 -->
      <div class="video-section">
        <div class="video-player">
          <TCPlayer
            v-if="room.stream_url"
            :src="room.stream_url"
            :poster="room.cover_image"
            :autoplay="true"
            width="100%"
            height="500px"
          />
          <div v-else class="no-stream">
            <el-icon size="64"><VideoCamera /></el-icon>
            <p>主播暂未开播</p>
          </div>
        </div>
        
        <!-- 房间信息 -->
        <div class="room-info">
          <div class="room-header">
            <h1 class="room-title">{{ room.title }}</h1>
            <div class="room-stats">
              <span class="viewer-count">
                <el-icon><View /></el-icon>
                {{ formatNumber(room.viewer_count) }}
              </span>
              <span class="like-count">
                <el-icon><Star /></el-icon>
                {{ formatNumber(room.like_count) }}
              </span>
            </div>
          </div>
          
          <div class="streamer-info">
            <el-avatar :src="room.user?.avatar" :size="40">
              {{ room.user?.nickname?.charAt(0) }}
            </el-avatar>
            <div class="streamer-details">
              <div class="streamer-name">{{ room.user?.nickname }}</div>
              <div class="streamer-followers">粉丝: {{ formatNumber(room.user?.followers_count || 0) }}</div>
            </div>
            <div class="actions">
              <el-button
                v-if="!isFollowing"
                type="primary"
                @click="handleFollow"
              >
                关注
              </el-button>
              <el-button
                v-else
                @click="handleUnfollow"
              >
                已关注
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 聊天区域 -->
      <div class="chat-section">
        <div class="chat-header">
          <h3>聊天室</h3>
          <span class="online-count">在线: {{ onlineCount }}</span>
        </div>
        
        <div class="chat-messages" ref="chatMessagesRef">
          <div
            v-for="message in chatMessages"
            :key="message.id"
            class="chat-message"
            :class="{ 'system-message': message.type === 'system' }"
          >
            <div v-if="message.type === 'user'" class="user-message">
              <el-avatar :src="message.user?.avatar" :size="24">
                {{ message.user?.nickname?.charAt(0) }}
              </el-avatar>
              <div class="message-content">
                <span class="username">{{ message.user?.nickname }}:</span>
                <span class="text">{{ message.content }}</span>
              </div>
            </div>
            <div v-else-if="message.type === 'gift'" class="gift-message">
              <el-icon><Present /></el-icon>
              <span>{{ message.user?.nickname }} 送出了 {{ message.gift?.name }}</span>
            </div>
            <div v-else class="system-text">
              {{ message.content }}
            </div>
          </div>
        </div>
        
        <div class="chat-input">
          <el-input
            v-model="messageInput"
            placeholder="说点什么..."
            @keyup.enter="sendMessage"
          >
            <template #append>
              <el-button @click="sendMessage">发送</el-button>
            </template>
          </el-input>
        </div>
        
        <!-- 礼物面板 -->
        <div class="gift-panel">
          <div class="gift-header">
            <h4>礼物</h4>
          </div>
          <div class="gift-list">
            <div
              v-for="gift in gifts"
              :key="gift.id"
              class="gift-item"
              @click="sendGift(gift)"
            >
              <el-image :src="gift.icon" style="width: 40px; height: 40px" />
              <span class="gift-name">{{ gift.name }}</span>
              <span class="gift-price">¥{{ gift.price }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import TCPlayer from '@/components/TCPlayer.vue'
import { liveApi } from '@/services/live'
import { useUserStore } from '@/stores/user'
import { initChat, sendChatMessage, onChatMessage } from '@/utils/chat'

const route = useRoute()
const userStore = useUserStore()

const room = ref({
  id: 0,
  title: '',
  description: '',
  cover_image: '',
  stream_url: '',
  viewer_count: 0,
  like_count: 0,
  user: {
    id: 0,
    nickname: '',
    avatar: '',
    followers_count: 0
  }
})

const isFollowing = ref(false)
const onlineCount = ref(0)
const chatMessages = ref([])
const messageInput = ref('')
const gifts = ref([])
const chatMessagesRef = ref()

// 获取房间信息
const getRoomInfo = async () => {
  try {
    const roomId = route.params.roomId as string
    const data = await liveApi.getRoomInfo(parseInt(roomId))
    room.value = data
  } catch (error) {
    console.error('获取房间信息失败:', error)
    ElMessage.error('获取房间信息失败')
  }
}

// 获取礼物列表
const getGifts = async () => {
  try {
    // TODO: 调用API获取礼物列表
    gifts.value = [
      {
        id: 1,
        name: '玫瑰',
        price: 1,
        icon: '/images/gifts/rose.png'
      },
      {
        id: 2,
        name: '跑车',
        price: 100,
        icon: '/images/gifts/car.png'
      }
    ]
  } catch (error) {
    console.error('获取礼物列表失败:', error)
  }
}

// 关注主播
const handleFollow = async () => {
  try {
    if (!userStore.isLoggedIn) {
      ElMessage.warning('请先登录')
      return
    }
    
    // TODO: 调用API关注主播
    isFollowing.value = true
    ElMessage.success('关注成功')
  } catch (error) {
    console.error('关注失败:', error)
    ElMessage.error('关注失败')
  }
}

// 取消关注
const handleUnfollow = async () => {
  try {
    // TODO: 调用API取消关注
    isFollowing.value = false
    ElMessage.success('已取消关注')
  } catch (error) {
    console.error('取消关注失败:', error)
    ElMessage.error('取消关注失败')
  }
}

// 发送消息
const sendMessage = () => {
  if (!messageInput.value.trim()) return
  
  if (!userStore.isLoggedIn) {
    ElMessage.warning('请先登录')
    return
  }
  
  const message = {
    type: 'user',
    content: messageInput.value,
    user: userStore.user
  }
  
  sendChatMessage(message)
  messageInput.value = ''
}

// 发送礼物
const sendGift = async (gift: any) => {
  try {
    if (!userStore.isLoggedIn) {
      ElMessage.warning('请先登录')
      return
    }
    
    // TODO: 调用API发送礼物
    const giftMessage = {
      type: 'gift',
      user: userStore.user,
      gift: gift
    }
    
    sendChatMessage(giftMessage)
    ElMessage.success(`送出了${gift.name}`)
  } catch (error) {
    console.error('发送礼物失败:', error)
    ElMessage.error('发送礼物失败')
  }
}

// 滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (chatMessagesRef.value) {
      chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
    }
  })
}

// 格式化数字
const formatNumber = (num: number) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + 'w'
  } else if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'k'
  }
  return num.toString()
}

onMounted(async () => {
  await getRoomInfo()
  await getGifts()
  
  // 初始化聊天
  const roomId = route.params.roomId as string
  initChat(roomId)
  
  // 监听聊天消息
  onChatMessage((message: any) => {
    chatMessages.value.push(message)
    scrollToBottom()
  })
})

onUnmounted(() => {
  // 清理聊天连接
})
</script>

<style scoped>
.live-room {
  min-height: 100vh;
  background: #f5f7fa;
}

.room-container {
  max-width: 1400px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: 1fr 350px;
  gap: 20px;
  padding: 20px;
}

.video-section {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.video-player {
  position: relative;
}

.no-stream {
  height: 500px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  background: #000;
}

.room-info {
  padding: 20px;
}

.room-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.room-title {
  margin: 0;
  font-size: 20px;
  color: #303133;
}

.room-stats {
  display: flex;
  gap: 16px;
  color: #909399;
}

.viewer-count,
.like-count {
  display: flex;
  align-items: center;
  gap: 4px;
}

.streamer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.streamer-details {
  flex: 1;
}

.streamer-name {
  font-weight: 500;
  color: #303133;
}

.streamer-followers {
  font-size: 12px;
  color: #909399;
}

.chat-section {
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  height: fit-content;
  max-height: 800px;
}

.chat-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h3 {
  margin: 0;
  font-size: 16px;
}

.online-count {
  font-size: 12px;
  color: #909399;
}

.chat-messages {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
  max-height: 400px;
}

.chat-message {
  margin-bottom: 12px;
}

.user-message {
  display: flex;
  gap: 8px;
}

.message-content {
  flex: 1;
}

.username {
  font-weight: 500;
  color: #409eff;
  margin-right: 4px;
}

.gift-message {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #f56c6c;
  font-weight: 500;
}

.system-message {
  text-align: center;
  color: #909399;
  font-size: 12px;
}

.chat-input {
  padding: 16px;
  border-top: 1px solid #e4e7ed;
}

.gift-panel {
  border-top: 1px solid #e4e7ed;
}

.gift-header {
  padding: 12px 16px;
  background: #f5f7fa;
}

.gift-header h4 {
  margin: 0;
  font-size: 14px;
}

.gift-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  padding: 12px;
}

.gift-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s;
}

.gift-item:hover {
  background: #f5f7fa;
}

.gift-name {
  font-size: 12px;
  margin-top: 4px;
}

.gift-price {
  font-size: 10px;
  color: #f56c6c;
  font-weight: 500;
}

@media (max-width: 768px) {
  .room-container {
    grid-template-columns: 1fr;
    padding: 10px;
  }
}
</style>
